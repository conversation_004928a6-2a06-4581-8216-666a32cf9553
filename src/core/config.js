// export const protocol = window.location.protocol+'//';

export const protocol = 'https://'
// export const protocol = 'http://'

export const getHost = () => {
    let host = window.location.host
    // return "api-sandbox.getmebab.com"

    if (host === "jaysmetal-admin.web.app" ) {
        // return env_config("REACT_APP_LIVE_DOMAIN")
        return "api-sandbox.getmebab.com"

    }
    else if(host === "localhost:3000"){
        return "api-sandbox.getmebab.com"
        // return  env_config("REACT_APP_DEV_DOMAIN")
    }
    else{
        return "api-sandbox.getmebab.com"
        // return env_config("REACT_APP_SANDBOX_DOMAIN")
    }
}


export const url = protocol + getHost()