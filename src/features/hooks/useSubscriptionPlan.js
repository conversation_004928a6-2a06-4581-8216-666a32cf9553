import { useState, useCallback } from "react";

const useSubscriptionPlan = () => {
  const [loading, setLoading] = useState(false);
  const [planDetails, setPlanDetails] = useState(null);
  const [error, setError] = useState(null);
  const baseUrl = "https://api-sandbox.getmebab.com";

  const fetchSubscriptionPlan = useCallback(async (eid) => {
    setLoading(true);
    try {
      // const data = getPlanByType(orderType);
      // console.log("fetchUrls@@@@@@@@", `${baseUrl}/order/?eid=${eid}`);

      const response = await fetch(`${baseUrl}/order/?eid=${eid}`);
      const data = await response.json();
      console.log("response data", data.membershipname);
      setPlanDetails(data);
    } catch (error) {
      setError(error);
      console.error("Error fetching subscription plan:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    planDetails,
    fetchSubscriptionPlan,
    // handlePayment,
  };
};

export default useSubscriptionPlan;
