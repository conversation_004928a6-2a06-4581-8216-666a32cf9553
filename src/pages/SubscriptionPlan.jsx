import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Header from "../components/common/Header";
import Footer from "../components/common/Footer";
import PriceCard from "../components/subscription/PriceCard";
// import ComunitySection from "../components/subscription/ComunitySection";
import Popup from "../components/subscription/Popup";
import useSubscriptionPlan from "../features/hooks/useSubscriptionPlan";
import { useSearchParams } from "react-router-dom";

function SubscriptionPlan() {
  const [showPopup, setShowPopup] = useState(false);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const platform = searchParams.get("platform");
  const orderType = searchParams.get("order_type");
  const eid = searchParams.get("eid");
  const { fetchSubscriptionPlan, planDetails, handlePayment, loading } =
    useSubscriptionPlan();
  console.log("loading", loading);
  console.log("params:", { platform, orderType, eid });
  useEffect(() => {
    console.log("planType:", orderType);
    fetchSubscriptionPlan(eid);
  }, [orderType, fetchSubscriptionPlan]);

  const handleCheckoutClick = (e) => {
    e.preventDefault();
    setShowPopup(true);
  };

  const handlePaymentStatus = (status) => {
    setShowPopup(false);
    if (status === "success") {
      handlePayment(status);
      navigate("/payment-success");
    } else {
      navigate("/payment-failed");
    }
  };
  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "100vh",
          fontSize: "18px",
        }}
      >
        Loading...
      </div>
    );
  }
  return (
    <div>
      {platform !== "app" ? <Header /> : null}
      <section
        className="page-header single-header bg_img oh"
        data-background="assets/images/page-header.png"
      >
        <div className="bottom-shape d-none d-md-block">
          <img src="assets/css/img/page-header2.png" alt="css" />
        </div>
      </section>
      {/* <!--============= Header Section Ends Here =============--> */}
      <PriceCard
        handleCheckoutClick={handleCheckoutClick}
        planDetails={planDetails}
      />
      {/* <ComunitySection /> */}
      {/* Payment Status Popup */}
      {showPopup && (
        <Popup
          setShowPopup={setShowPopup}
          handlePaymentStatus={handlePaymentStatus}
        />
      )}
      {platform !== "app" ? <Footer /> : null}
      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
      <script src="/assets/js/jquery.min.js"></script>
    </div>
  );
}
export default SubscriptionPlan;
