import React from "react";

function PaymentFailed() {
  return (
    <>
      <div
        className="failed-section bg_img"
        data-background="assets/images/error/error-bg.jpg"
        style={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
        }}
      >
        <div className="container">
          <div
            className="failed-wrapper wow bounceInDown"
            data-wow-duration=".7s"
            data-wow-delay="1s"
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              textAlign: "center",
              maxWidth: "600px",
              margin: "0 auto",
              padding: "40px 30px",
              borderRadius: "15px",
              backdropFilter: "blur(10px)",
            }}
          >
            <div className="failed-icon" style={{ marginBottom: "20px" }}>
              <svg
                width="80"
                height="80"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="#dc3545"
                  strokeWidth="2"
                  fill="#dc3545"
                  fillOpacity="0.1"
                />
                <path
                  d="m15 9-6 6"
                  stroke="#dc3545"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="m9 9 6 6"
                  stroke="#dc3545"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <h1
              className="title"
              style={{ textAlign: "center", marginBottom: "15px" }}
            >
              Payment Failed
            </h1>
            <h3
              className="subtitle"
              style={{ textAlign: "center", marginBottom: "20px" }}
            >
              We couldn't process your payment
            </h3>
            <p
              className="description"
              style={{ textAlign: "center", marginBottom: "30px" }}
            >
              Your payment could not be processed at this time. Please check
              your payment details and try again.
            </p>
            <div
              className="failed-actions"
              style={{
                display: "flex",
                gap: "15px",
                justifyContent: "center",
                flexWrap: "wrap",
                marginBottom: "40px",
              }}
            >
              <a href="/plans" className="button-5 primary">
                Try Again
              </a>
              <a href="/contact" className="button-5 secondary">
                Contact Support
              </a>
            </div>
          </div>

          <div
            className="failed-details"
            style={{
              display: "flex",
              justifyContent: "center",
              textAlign: "center",
            }}
          ></div>
        </div>
      </div>
    </>
  );
}

export default PaymentFailed;
