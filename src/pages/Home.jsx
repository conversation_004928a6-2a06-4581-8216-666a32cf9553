import React, { useEffect } from "react";
import Footer from "../components/common/Footer";
import Header from "../components/common/Header";
import Banner from "../components/home/<USER>";
// import Sponsor from "../components/home/<USER>";
import AmazingFeatures from "../components/home/<USER>";
import HowItWorks from "../components/home/<USER>";
// import Newsletter from "../components/home/<USER>";
// import Pricing from "../components/home/<USER>";
import Coverage from "../components/home/<USER>";
import AppFeatures from "../components/home/<USER>";

function Home() {
  useEffect(() => {
    // Hide preloader after mount
    const preloader = document.querySelector(".preloader");
    if (preloader) {
      preloader.style.display = "none";
    }
    window.initPageEffects();
  }, []);
  return (
    <div>
      {/*============= ScrollToTop Section Starts Here =============*/}
      <div className="preloader">
        <div className="preloader-inner">
          <div className="preloader-icon">
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
      <a href="#0" className="scrollToTop">
        <i className="fas fa-angle-up"></i>
      </a>
      <div className="overlay"></div>

      <Header />

      <Banner />

      {/* <Sponsor /> */}

      <AmazingFeatures />

      <HowItWorks />

      {/* <Newsletter /> */}
      {/*============= Feature Section Starts Here =============*/}
      <AppFeatures />
      {/*============= Feature Section Ends Here =============*/}
      {/* <Pricing /> */}
      <Coverage />
      <Footer />
      {/* =================== RTL Feature Section Starts Here =================== */}
      {/* <div className="swap-area active">
        <div className="chorka">
          <img src="assets/images/gear.png" alt="img" />
        </div>
        <div className="swap-item">
          <a href="https://softivuspro.com/mosto/demo/ltr/light/index.html">
            Light
          </a>
        </div>
        <div className="swap-item">
          <a href="https://softivuspro.com/mosto/demo/rtl/light/index.html">
            RTL
          </a>
        </div>
      </div> */}
      {/* =================== RTL Feature Section Ends Here =================== */}
    </div>
  );
}

export default Home;
