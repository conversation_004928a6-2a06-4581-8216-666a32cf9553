import React from "react";

function PaymentSuccess() {
  return (
    <>
      <div
        className="success-section bg_img"
        data-background="assets/images/success/success-bg.jpg"
        style={{
          minHeight: "100vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
        }}
      >
        <div className="container">
          <div
            className="success-wrapper wow bounceInDown"
            data-wow-duration=".7s"
            data-wow-delay="1s"
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              textAlign: "center",
              maxWidth: "600px",
              margin: "0 auto",
            }}
          >
            <div className="success-icon" style={{ marginBottom: "20px" }}>
              <svg
                width="80"
                height="80"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="#28a745"
                  strokeWidth="2"
                  fill="#28a745"
                  fillOpacity="0.1"
                />
                <path
                  d="m9 12 2 2 4-4"
                  stroke="#28a745"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <h1
              className="title"
              style={{ textAlign: "center", marginBottom: "15px" }}
            >
              Payment Successful!
            </h1>
            <h3
              className="subtitle"
              style={{ textAlign: "center", marginBottom: "20px" }}
            >
              Thank you for your purchase
            </h3>
            <p
              className="description"
              style={{ textAlign: "center", marginBottom: "30px" }}
            >
              Your payment has been processed successfully. You will receive a
              confirmation email shortly.
            </p>
            <div
              className="success-actions"
              style={{
                display: "flex",
                gap: "15px",
                justifyContent: "center",
                flexWrap: "wrap",
                marginBottom: "40px",
              }}
            >
              <a href="/" className="button-5 primary">
                Go to App
              </a>
            </div>
          </div>

          <div
            className="success-details"
            style={{
              display: "flex",
              justifyContent: "center",
              textAlign: "center",
            }}
          >
            <div
              className="detail-card wow fadeInUp"
              data-wow-duration=".5s"
              data-wow-delay="1.2s"
              style={{
                maxWidth: "500px",
                textAlign: "center",
              }}
            >
              <h4 style={{ textAlign: "center", marginBottom: "20px" }}>
                What's Next?
              </h4>
              <ul style={{ textAlign: "left", display: "inline-block" }}>
                <li>Check your email for payment confirmation</li>
                <li>Your premium features are now active</li>
                <li>Start exploring enhanced dating features</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default PaymentSuccess;
