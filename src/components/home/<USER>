import React from "react";

const Banner = () => {
  return (
    <div
      className="banner-1 bg_img oh"
      data-background="assets/images/banner/banner-bg-1.jpg"
    >
      <div className="dot-1 d-none d-lg-block">
        <img src="assets/images/banner/dot-big.png" alt="banner" />
      </div>
      <div className="dot-2 d-none d-lg-block">
        <img src="assets/images/banner/dot-big.png" alt="banner" />
      </div>
      <div className="dot-3">
        <img src="assets/images/banner/dot-sm.png" alt="banner" />
      </div>
      <div className="dot-4">
        <img src="assets/images/banner/dot-sm.png" alt="banner" />
      </div>
      <div className="banner-1-shape d-none d-lg-block">
        <img src="assets/css/img/banner1-shape.png" alt="css" />
      </div>
      <div className="container">
        <div className="row align-items-center">
          <div className="col-lg-6">
            <div className="banner-content-1 cl-white">
              <h1 className="title">Find Your Perfect Match</h1>
              <p>
                Join millions of singles who've discovered meaningful
                connections on Spark. Swipe smart, match better, love deeper.
              </p>
              <div className="banner-button-group">
                <a href="#0" className="button-4">
                  Download Free
                </a>
                <a href="#0" className="button-4 active">
                  Explore Features
                </a>
              </div>
            </div>
          </div>

          <div className="col-lg-5">
            {/* <div className="banner-1-slider-wrapper">
              <div className="banner-1-slider owl-carousel owl-theme">
                <div className="banner-thumb">
                  <img src="assets/images/banner/banner1-1.png" alt="banner" />
                </div>
                <div className="banner-thumb">
                  <img src="assets/images/banner/banner1-2.png" alt="banner" />
                </div>
                <div className="banner-thumb">
                  <img src="assets/images/banner/banner1-3.png" alt="banner" />
                </div>
                <div className="banner-thumb">
                  <img src="assets/images/banner/banner1-1.png" alt="banner" />
                </div>
                <div className="banner-thumb">
                  <img src="assets/images/banner/banner1-2.png" alt="banner" />
                </div>
                <div className="banner-thumb">
                  <img src="assets/images/banner/banner1-3.png" alt="banner" />
                </div>
              </div>
              <div className="ban-click">
                <div className="thumb">
                  <img src="assets/images/banner/click.png" alt="banner" />
                </div>
                <span className="cl-white">Click Me</span>
              </div>
              <div className="arrow">
                <img src="assets/images/banner/arrow.png" alt="banner" />
              </div>
            </div> */}
            <div
              className="banner-1-slider-wrapper"
              style={{ height: "700px" }}
            >
              <div
                className="banner-thumb"
                style={{ width: "100%", height: "100%" }}
              >
                <img
                  src="/assets/images/banner/banner1-1.png"
                  alt="banner"
                  style={{
                    width: "100%",
                    height: "100%",
                    objectFit: "contain",
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Banner;
