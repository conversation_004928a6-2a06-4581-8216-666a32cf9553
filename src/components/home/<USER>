import React from "react";

const HowItWorks = () => {
  return (
    <div className="how-section padding-bottom padding-top" id="how">
      {" "}
      <div className="container">
        <div className="section-header">
          {/* <h5 className="cate">Describe Your App</h5> */}
          <h2 className="title">Let’s See How It Work</h2>
          <p>It's easier than you think.Follow 3 simple easy steps</p>
        </div>
        <div className="row justify-content-xl-between justify-content-center mb-40-none">
          <div className="col-lg-4 col-sm-6 col-xl-3">
            <div className="how-item">
              <div className="how-thumb">
                <img src="assets/images/how/how1.png" alt="how" />
              </div>
              <div className="how-content">
                <a href="#0" className="button-3 active">
                  Download App
                </a>
                <ul className="download-options">
                  <li>
                    <a href="#0">
                      <i className="fab fa-windows"></i>
                    </a>
                  </li>
                  <li>
                    <a href="#0" className="active">
                      <i className="fab fa-apple"></i>
                    </a>
                  </li>
                  <li>
                    <a href="#0">
                      <i className="fab fa-android"></i>
                    </a>
                  </li>
                </ul>
                <p>Download App either for Windows,Mac or Android</p>
              </div>
            </div>
          </div>
          <div className="col-lg-4 col-sm-6 col-xl-3">
            <div className="how-item">
              <div className="how-thumb">
                <img src="assets/images/how/how2.png" alt="how" />
              </div>
              <div className="how-content">
                <a href="#0" className="button-3">
                  Create Account
                </a>
                <h5 className="title">14 days free trial</h5>
                <p>Sign up for Mosta account. One account for all devices</p>
              </div>
            </div>
          </div>
          <div className="col-lg-4 col-sm-6 col-xl-3">
            <div className="how-item">
              <div className="how-thumb">
                <img src="assets/images/how/how3.png" alt="how" />
              </div>
              <div className="how-content">
                <a href="#0" className="button-3">
                  Enjoy The App
                </a>
                <h5 className="title">
                  Have any questions check our <a href="#0">FAQs</a>
                </h5>
                <p>Explore and share GetmeBab</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;
