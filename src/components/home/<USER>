import React from "react";

const Newsletter = () => {
  return (
    <div
      className="newslater-section oh bg_img pos-rel mb-120 mb-max-lg-0"
      data-background="assets/images/bg/newslater2.jpg"
    >
      <div className="top-shape d-none d-lg-block">
        <img src="assets/css/img/top-shape2.png" alt="css" />
      </div>
      <div className="bottom-shape d-none d-lg-block">
        <img src="assets/css/img/bottom-shape2.png" alt="css" />
      </div>
      <div className="container">
        <div className="row align-items-center">
          <div className="col-lg-6">
            <div className="newslater-area padding-bottom padding-top">
              <div className="section-header left-style cl-white">
                <h5 className="cate">Be the first to know</h5>
                <h2 className="title">About New Features</h2>
                <p>
                  If you want to receive monthly updates from us just pop your
                  email in the box. You can unsubscribe at any time. Your
                  privacy & personal information will be treated.
                </p>
              </div>
              <form className="subscribe-form">
                <input type="text" placeholder="Enter Your Email" />
                <button type="submit">subscribe</button>
              </form>
            </div>
          </div>
          <div className="col-lg-6 d-none d-lg-block">
            <div className="subscribe-thumb-2">
              <img
                src="assets/images/newslater/newslater2.png"
                alt="newslater"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Newsletter;
