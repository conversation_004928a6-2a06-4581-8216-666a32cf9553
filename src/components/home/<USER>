import React from "react";

const AmazingFeatures = () => {
  return (
    <div className="amazing-feature-section pos-rel" id="feature">
      <div className="shape-container oh">
        <div
          className="bg_img feature-background"
          data-background="assets/images/bg/amazing-feature-bg.jpg"
        ></div>
        <div className="feature-top-shape d-none d-lg-block">
          <img src="assets/css/img/feature-shape.png" alt="css" />
        </div>
      </div>
      <div className="topper-feature oh padding-top">
        <div className="container">
          <div className="row">
            <div className="col-lg-6">
              <div className="section-header left-style mb-lg-0 white-lg-black pos-rel">
                <h5 className="cate">Explore Amazing Features</h5>
                <h2 className="title">That will boost your productivity</h2>
                <p>
                  With our wide range of features, you can create a custom app
                  no matter what your niche: restaurant, Realtor, small
                  business, rock band, and all the rest!
                </p>
                <div className="downarrow d-none d-lg-block">
                  <img
                    src="assets/images/feature/downarrow.png"
                    alt="feature"
                  />
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <a
                href="https://www.youtube.com/watch?v=Djz8Nc0Qxwk"
                className="feature-video-area popup"
              >
                <div className="thumb">
                  <img
                    src="assets/images/feature/fature-video.png"
                    alt="feature"
                  />
                </div>
                <div className="button-area">
                  <h4 className="title">Watch Videos</h4>
                  <div className="video-button">
                    <i className="flaticon-play"></i>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div className="amazing-feature-bottom padding-top padding-bottom pb-lg-0 pt-lg-0">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 col-xl-7">
              <div className="section-header left-style cl-white">
                <h5 className="cate">A Collection of Prominent Features</h5>
                <h2 className="title">
                  Delivering Exceptional User Experiences.
                </h2>
                <p>
                  Numerous features make it possible to customize the system in
                  accordance with all your needs.
                </p>
              </div>
            </div>
            <div className="col-12">
              <div className="row justify-content-center mb-30-none">
                <div className="col-sm-6 col-lg-3">
                  <div className="am-item">
                    <div className="am-thumb">
                      <img
                        src="assets/images/feature/am-fea1.png"
                        alt="feature"
                      />
                    </div>
                    <div className="am-content">
                      <h5 className="title">Free Trial</h5>
                    </div>
                  </div>
                </div>
                <div className="col-sm-6 col-lg-3">
                  <div className="am-item active">
                    <div className="am-thumb">
                      <img
                        src="assets/images/feature/am-fea2.png"
                        alt="feature"
                      />
                    </div>
                    <div className="am-content">
                      <h5 className="title">Secure Data</h5>
                    </div>
                  </div>
                </div>
                <div className="col-sm-6 col-lg-3">
                  <div className="am-item">
                    <div className="am-thumb">
                      <img
                        src="assets/images/feature/am-fea3.png"
                        alt="feature"
                      />
                    </div>
                    <div className="am-content">
                      <h5 className="title">Quick Access</h5>
                    </div>
                  </div>
                </div>
                <div className="col-sm-6 col-lg-3">
                  <div className="am-item">
                    <div className="am-thumb">
                      <img
                        src="assets/images/feature/am-fea4.png"
                        alt="feature"
                      />
                    </div>
                    <div className="am-content">
                      <h5 className="title">24h Support</h5>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AmazingFeatures;
