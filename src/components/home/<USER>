import React, { useEffect, useRef } from "react";

const AppFeatures = () => {
  const sliderRef = useRef(null);
  const owlInstance = useRef(null);

  useEffect(() => {
    // Initialize the feature slider when component mounts
    if (window.initFeatureSlider && sliderRef.current) {
      // Small timeout to ensure DOM is fully rendered
      setTimeout(() => {
        owlInstance.current = window.initFeatureSlider(".feat-slider");
      }, 100);
    }

    // Cleanup function to destroy carousel when component unmounts
    return () => {
      if (owlInstance.current) {
        owlInstance.current.trigger("destroy.owl.carousel");
      }
    };
  }, []);

  return (
    <div className="feature-section padding-top padding-bottom oh pos-rel">
      <div className="feature-shapes d-none d-lg-block">
        <img src="assets/images/feature/feature-shape.png" alt="feature" />
      </div>
      <div className="container">
        <div className="section-header mw-725">
          {/* <h5 className="cate">Extra Crazy Features that will</h5> */}
          <h2 className="title">Why Choose GetmeBab?</h2>
          <p>
            Experience dating like never before with features designed to help
            you find genuine connections
          </p>
        </div>
        <div className="row">
          <div className="col-lg-5 rtl">
            <div className="feature--thumb pr-xl-4 ltr" ref={sliderRef}>
              <div
                className="feat-slider owl-carousel owl-theme"
                data-slider-id="1"
              >
                <div className="main-thumb">
                  <img
                    src="assets/images/feature/pro-main2.png"
                    alt="feature"
                  />
                </div>
                <div className="main-thumb">
                  <img src="assets/images/feature/pro-main.png" alt="feature" />
                </div>
                <div className="main-thumb">
                  <img
                    src="assets/images/feature/pro-main3.png"
                    alt="feature"
                  />
                </div>
                <div className="main-thumb">
                  <img
                    src="assets/images/feature/pro-main4.png"
                    alt="feature"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="col-lg-7">
            <div
              className="feature-wrapper mb-30-none owl-thumbs"
              data-slider-id="1"
            >
              <div className="feature-item">
                <div className="feature-thumb">
                  <div className="thumb">
                    <img src="assets/images/feature/pro1.png" alt="feature" />
                  </div>
                </div>
                <div className="feature-content">
                  <h4 className="title">Smart Matching</h4>
                  <p>
                    Our AI-powered algorithm learns your preferences to suggest
                    the most compatible matches based on shared interests,
                    values, and lifestyle.
                  </p>
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-thumb">
                  <div className="thumb">
                    <img src="assets/images/feature/pro2.png" alt="feature" />
                  </div>
                </div>
                <div className="feature-content">
                  <h4 className="title">Safe & Secure</h4>
                  <p>
                    Photo verification, background checks, and robust privacy
                    controls ensure you can focus on connecting safely with real
                    people.
                  </p>
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-thumb">
                  <div className="thumb">
                    <img src="assets/images/feature/pro3.png" alt="feature" />
                  </div>
                </div>
                <div className="feature-content">
                  <h4 className="title">Meaningful Conversations</h4>
                  <p>
                    The satisfaction of users is the most important and the
                    focus is on usability and completeness
                  </p>
                </div>
              </div>
              <div className="feature-item">
                <div className="feature-thumb">
                  <div className="thumb">
                    <img src="assets/images/feature/pro4.png" alt="feature" />
                  </div>
                </div>
                <div className="feature-content">
                  <h4 className="title">Premium Experience</h4>
                  <p>
                    Enjoy unlimited likes, advanced filters, read receipts, and
                    exclusive features that give you the edge in finding your
                    perfect match.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppFeatures;
