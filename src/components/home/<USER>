import React from "react";

const Coverage = () => {
  return (
    <div className="coverage-section padding-top padding-bottom" id="coverage">
      <div className="container">
        <div className="row align-items-center">
          <div className="col-lg-7">
            <div className="section-header left-style coverage-header">
              <h5 className="cate">Our stats say more than any words</h5>
              <h2 className="title">Apps Without Borders</h2>
              <p>
                GetmeBab app are growing by 300% every year with a steady love
                from users around the world. We are also close to achieving 10
                million cumulative downloads.
              </p>
            </div>
          </div>
          <div className="col-lg-5">
            <div className="coverage-right-area text-lg-end">
              <div className="rating-area">
                <div className="ratings">
                  <span>
                    <i className="fas fa-star"></i>
                  </span>
                  <span>
                    <i className="fas fa-star"></i>
                  </span>
                  <span>
                    <i className="fas fa-star"></i>
                  </span>
                  <span>
                    <i className="fas fa-star"></i>
                  </span>
                  <span>
                    <i className="fas fa-star"></i>
                  </span>
                </div>
                <span className="average">5.0 / 5.0</span>
              </div>
              <h2 className="amount">312,921+</h2>
              <a href="#0">
                Total User Reviews <i className="fas fa-paper-plane"></i>
              </a>
            </div>
          </div>
        </div>
        <div
          className="coverage-wrapper bg_img bg_auto bg_auto"
          data-background="assets/images/bg/world-map.png"
        >
          <div className="border-item-1">
            <span className="name">North America</span>
            <h2 className="title">70.7%</h2>
          </div>
          <div className="border-item-2">
            <span className="name">Asia</span>
            <h2 className="title">14.4%</h2>
          </div>
          <div className="border-item-3">
            <span className="name">North Europe</span>
            <h2 className="title">8.4%</h2>
          </div>
          <div className="border-item-4">
            <span className="name">South America</span>
            <h2 className="title">1.8%</h2>
          </div>
          <div className="border-item-5">
            <span className="name">Africa</span>
            <h2 className="title">1.8%</h2>
          </div>
          <div className="border-item-6">
            <span className="name">Australia</span>
            <h2 className="title">3%</h2>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Coverage;
