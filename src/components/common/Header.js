import React, { useEffect, useState } from "react";
import { NavLink } from "react-router-dom";

const Header = () => {
  const [menuActive, setMenuActive] = useState(false);

  useEffect(() => {
    // Call the header scroll function from main.js
    if (window.initHeaderScroll) {
      window.initHeaderScroll();
    }
  }, []);

  const toggleMenu = () => {
    setMenuActive(!menuActive);

    // Toggle overlay active class
    document.querySelector(".overlay")?.classList.toggle("active");
  };

  return (
    <div className="header-section">
      <div className="container">
        <div className="header-wrapper">
          <div className="logo">
            <NavLink to="/">
              <img src="assets/images/logo/logo.png" alt="logo" />
            </NavLink>
          </div>
          <ul className={`menu ${menuActive ? "active" : ""}`}>
            <li>
              <NavLink to="/">Home</NavLink>
            </li>
            <li>
              <NavLink to="/about">About</NavLink>
            </li>
            <li>
              <a href="contact.html">contact</a>
            </li>
            <li className="d-sm-none">
              <a href="#0" className="m-0 header-button">
                Get Mosto
              </a>
            </li>
          </ul>
          <div
            className={`header-bar d-lg-none ${menuActive ? "active" : ""}`}
            onClick={toggleMenu}
          >
            <span></span>
            <span></span>
            <span></span>
          </div>
          {/* <div className="header-right">
            <select className="select-bar">
              <option value="en">En</option>
              <option value="Bn">Bn</option>
              <option value="pk">Pk</option>
              <option value="Fr">Fr</option>
            </select>
            <a href="#0" className="header-button d-none d-sm-inline-block">
              Get Mosto
            </a>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default Header;
