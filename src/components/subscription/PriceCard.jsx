import React from "react";

function PriceCard({ handleCheckoutClick, planDetails }) {
  console.log("planDetails:", planDetails?.results[0].membershipname);
  return (
    <section className="pricing-section oh padding-bottom-2 single-pricing">
      <div className="container">
        <div className="section-header cl-white mw-100 mb-4">
          <h2 className="title mt-0">
            You are about to purchase {planDetails?.results[0].membershipname}{" "}
            Plan
          </h2>
          <p>
            You are just a few steps away to experience your real partner{" "}
            <i className="fas fa-heart"></i>
          </p>
        </div>
        <div className="row justify-content-center">
          <div className="col-lg-4 col-md-6">
            <div className="pricing-item-2">
              <h5 className="cate">{planDetails?.results[0].membershipname}</h5>
              <div className="thumb">
                {planDetails?.results[0].order_type ===
                "membership_subscription" ? (
                  <img src="assets/images/pricing/pricing4.png" alt="pricing" />
                ) : planDetails?.planType === "Coin Recharge" ? (
                  <img
                    src="assets/images/subscription/coin.png"
                    alt="pricing"
                  />
                ) : (
                  <img
                    src="assets/images/subscription/topupfeature.png"
                    alt="pricing"
                  />
                )}
              </div>
              <h2 className="title">
                <sup>$</sup>
                {planDetails?.results[0].total_price}
              </h2>
              <span className="info">Per Month</span>
              {/* <ul className="pricing-content-3">
                {planDetails?.features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul> */}
              <a href="#0" className="get-button" onClick={handleCheckoutClick}>
                Checkout<i className="flaticon-right"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default PriceCard;
