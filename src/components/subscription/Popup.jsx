import React from "react";

function Popup({ setShowPopup, handlePaymentStatus }) {
  return (
    <div
      className="popup-overlay"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 9999,
      }}
    >
      <div
        className="popup-content"
        style={{
          backgroundColor: "white",
          padding: "40px 30px",
          borderRadius: "15px",
          boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
          textAlign: "center",
          maxWidth: "400px",
          width: "90%",
        }}
      >
        <h3 style={{ marginBottom: "20px", color: "#333" }}>Payment Status</h3>
        <p style={{ marginBottom: "30px", color: "#666" }}>
          Choose the payment outcome:
        </p>
        <div style={{ display: "flex", gap: "15px", justifyContent: "center" }}>
          <button
            onClick={() => handlePaymentStatus("success")}
            style={{
              backgroundColor: "#28a745",
              color: "white",
              border: "none",
              padding: "12px 24px",
              borderRadius: "8px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            Success
          </button>
          <button
            onClick={() => handlePaymentStatus("failed")}
            style={{
              backgroundColor: "#dc3545",
              color: "white",
              border: "none",
              padding: "12px 24px",
              borderRadius: "8px",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold",
            }}
          >
            Failed
          </button>
        </div>
        <button
          onClick={() => setShowPopup(false)}
          style={{
            backgroundColor: "transparent",
            color: "#666",
            border: "none",
            marginTop: "20px",
            cursor: "pointer",
            textDecoration: "underline",
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
}

export default Popup;
