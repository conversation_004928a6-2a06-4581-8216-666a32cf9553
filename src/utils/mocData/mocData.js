export const mocData = {
  subscriptionPlans: {
    diamond: {
      planType: "Diamond",
      price: 30,
      features: [
        "Message before Matching 💬",
        "Prioritized Likes 💖",
        "See Who Likes You 👍",
        "Weekly Super Likes 💕",
        "See who visited your profile 👀",
      ],
    },
    coinrecharge: {
      planType: "Coin Recharge",
      price: 20,
      features: [
        "Message before Matching 💬",
        "Prioritized Likes 💖",
        "See Who Likes You 👍",
        "Weekly Super Likes 💕",
        "See who visited your profile 👀",
      ],
    },
    topupfeature: {
      planType: "Top Up Feature",
      price: 10,
      features: [
        "Message before Matching 💬",
        "Prioritized Likes 💖",
        "See Who Likes You 👍",
        "Weekly Super Likes 💕",
        "See who visited your profile 👀",
      ],
    },
  },
};

export const getPlanByType = (planType) => {
  if (!planType) {
    return null;
  }

  return mocData.subscriptionPlans[planType] || null;
};
