@import url("https://fonts.googleapis.com/css?family=Josefin+Sans:300,300i,400,400i,500,500i,600,600i,700,700i|Open+Sans:400,400i,600,700&amp;display=swap");
/*
Template Name: Mosto - Software and App Landing Pages HTML Template
Template URI: http://pixner.net/mosto/
Author: pixelaxis
Author URI: https://themeforest.net/user/pixelaxis
Description: Mosto - Software and App Landing Pages HTML Template. Mosto is a Pixel-Perfect Powerful,
Mordern  Software and App Landing Pages HTML Template for all types of landing pages.
Version: 4.0.1
Text Domain: 
*/
/*
///// CSS Index \\
01. Reset CSS Start
---------------------------
02. General CSS
---------------------------
03. Section Header CSS
---------------------------
04. Social Icons CSS
---------------------------
05. Header Section CSS
---------------------------
06. Header Bar CSS
---------------------------
07. Banner Section CSS
---Banner Four Section
---Banner One Section
---Banner Section Two
---Banner Section Six
---Banner Section Five
---Banner Section Seven
---Banner Section Eight 
---Banner Section Nine
---Banner Section 10 
---Banner 11 Section
---Banner Section 12 
---Banner Section 13 
---banner Section 15
---Banner Section 14
---App Banner
---------------------------
08. Blog-Sedebar CSS
---------------------------
09. RTL CSS CSS
---------------------------
10. Trial Section CSS
---------------------------
11. Faq Section CSS
---------------------------
12. Testimonial Section CSS
---------------------------
13. Exclusive Section CSS
---------------------------
14. Colaboration Section CSS
---------------------------
15. Smart Watch Section CSS
---------------------------
16. Pricing Section CSS
---------------------------
17. Subscribe Seciton  CSS
---------------------------
18. Coverage Section CSS
---------------------------
19. Feature Section CSS
---------------------------
20. How Section CSS
---------------------------
21. Amazing Feature Section CSS
---------------------------
22. Sponsor Section CSS
---------------------------
23. Download Section CSS
---------------------------
24. Custom Plan CSS
---------------------------
25. Counter Section CSS
---Counter Section Two 
---------------------------
26. How Section Two  CSS
---------------------------
27. Real Data Section CSS
---------------------------
28. Convencing Feature CSS
---------------------------
29. Advance Feature Section CSS
---------------------------
30. To Access Section CSS
---------------------------
31. Work Section CSS
---------------------------
32. Feat Nav CSS
---------------------------
33. Feature Item Two  CSS
---------------------------
34. Experience Section CSS
---------------------------
35. Addon Section CSS
---------------------------
36. Cost Section CSS
---------------------------
37. Client Section CSS
---------------------------
38. How Section Three CSS
---------------------------
39. Faster Section CSS
---------------------------
40. Safe Section CSS
---------------------------
41. Pricing Section CSS
---------------------------
42. Recharge Section CSS
---------------------------
43. Help Section CSS
---------------------------
44. Testimonial Two Section CSS
---------------------------
45. App Video Section CSS
---------------------------
46. About Feature Section CSS
---------------------------
47. Pricing Section Four  CSS
---------------------------
48. Counter Section Four  CSS
---------------------------
49. Page Header CSS
---------------------------
50. About Section CSS
---------------------------
51. Team Section  CSS
---------------------------
52. Sponsor Slider Four  CSS
---------------------------
53. History Section CSS
---------------------------
54. Chart Section CSS
---------------------------
55. Team Single CSS
---------------------------
56. Partner Section CSS
---------------------------
57. User Counter CSS
---------------------------
58. Review Section CSS
---------------------------
59. Comunity Section CSS
---------------------------
60. Faq Section Two CSS
---------------------------
61. Privacy Section CSS
---------------------------
62. Coming Soon Section CSS
---------------------------
63. Account Section CSS
---------------------------
64. Error Section CSS
---------------------------
65. Feature Tab Section CSS
---------------------------
66. Single Pricing Section CSS
---------------------------
67. Estimate Plan Section CSS
---------------------------
68. Contact Section CSS
---------------------------
69. Do Section CSS
---------------------------
70. Map Section CSS
---------------------------
71. Extra CSS  CSS
---------------------------
72. All Animations CSS
---------------------------
73. Leave-Comment CSS
---------------------------
74. BLog Details Section CSS
---------------------------
*/
/*! HTML5 Boilerplate v7.0.1 | MIT License | https://html5boilerplate.com/ */
/* main.css 1.0.0 | MIT License | https://github.com/h5bp/main.css#readme */
/*
 * What follows is the result of much research on cross-browser styling.
 * Credit left inline and big thanks to Nicolas Gallagher, Jonathan Neal,
 * Kroc Camen, and the H5BP dev community and team.
 */
/* ==========================================================================
   Base styles: opinionated defaults
   ========================================================================== */
html {
  color: #222;
  font-size: 1em;
  line-height: 1.4;
}

/*
 * Remove text-shadow in selection highlight:
 * https://twitter.com/miketaylr/status/12228805301
 *
 * Vendor-prefixed and regular ::selection selectors cannot be combined:
 * https://stackoverflow.com/a/16982510/7133471
 *
 * Customize the background color to match your design.
 */
::-moz-selection {
  background: #b3d4fc;
  text-shadow: none;
}
::selection {
  background: #b3d4fc;
  text-shadow: none;
}

/*
 * A better looking default horizontal rule
 */
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0;
}

/*
 * Remove the gap between audio, canvas, iframes,
 * images, videos and the bottom of their containers:
 * https://github.com/h5bp/html5-boilerplate/issues/440
 */
audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle;
}

/*
 * Remove default fieldset styles.
 */
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

/*
 * Allow only vertical resizing of textareas.
 */
textarea {
  resize: vertical;
}

/* ==========================================================================
   Browser Upgrade Prompt
   ========================================================================== */
.browserupgrade {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

/* ==========================================================================
   Author's custom styles
   ========================================================================== */
/* ==========================================================================
   Helper classes
   ========================================================================== */
/*
 * Hide visually and from screen readers
 */
.hidden {
  display: none !important;
}

/*
* Hide only visually, but have it available for screen readers:
* https://snook.ca/archives/html_and_css/hiding-content-for-accessibility
*
* 1. For long content, line feeds are not interpreted as spaces and small width
*    causes content to wrap 1 word per line:
*    https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe
*/
.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
  white-space: nowrap;
  /* 1 */
}

/*
* Extends the .visuallyhidden class to allow the element
* to be focusable when navigated to via the keyboard:
* https://www.drupal.org/node/897638
*/
.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
  white-space: inherit;
}

/*
* Hide visually and from screen readers, but maintain layout
*/
.invisible {
  visibility: hidden;
}

/*
* Clearfix: contain floats
*
* For modern browsers
* 1. The space content is one way to avoid an Opera bug when the
*    `contenteditable` attribute is included anywhere else in the document.
*    Otherwise it causes space to appear at the top and bottom of elements
*    that receive the `clearfix` class.
* 2. The use of `table` rather than `block` is only necessary if using
*    `:before` to contain the top-margins of child elements.
*/
.clearfix:before,
.clearfix:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}

.clearfix:after {
  clear: both;
}

/* ==========================================================================
   EXAMPLE Media Queries for Responsive Design.
   These examples override the primary ('mobile first') styles.
   Modify as content requires.
   ========================================================================== */
@media only screen and (min-width: 35em) {
  /* Style adjustments for viewports that meet the condition */
}
@media print, (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 1.25dppx), (min-resolution: 120dpi) {
  /* Style adjustments for high resolution devices */
}
/* ==========================================================================
   Print styles.
   Inlined to avoid the additional HTTP request:
   https://www.phpied.com/delay-loading-your-print-css/
   ========================================================================== */
@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important;
    /* Black prints faster */
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  /*
   * Don't show links that are fragment identifiers,
   * or use the `javascript:` pseudo protocol
   */
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }
  pre {
    white-space: pre-wrap !important;
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  /*
   * Printing Tables:
   * http://css-discuss.incutio.com/wiki/Printing_Tables
   */
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
}
/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type=checkbox],
[type=radio] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type=search] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

html {
  font-size: 18px;
  scroll-behavior: smooth;
}

body {
  padding: 0;
  margin: 0;
  font-size: 18px;
  color: #bdb9f0;
  line-height: 28px;
  overflow-x: hidden;
  font-family: "Open Sans", sans-serif;
  background: #202342;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 700;
  margin: 0;
  line-height: 1.3;
  color: #ffffff;
  font-family: "Josefin Sans", sans-serif;
}
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
  color: #ffffff;
}
h1 a:hover,
h2 a:hover,
h3 a:hover,
h4 a:hover,
h5 a:hover,
h6 a:hover {
  color: #ee4730;
}

h1 {
  font-size: 42px;
}

h2 {
  font-size: 36px;
  line-height: 46px;
  margin-top: -16px;
}

h3 {
  font-size: 28px;
  line-height: 38px;
  margin-top: -14px;
  font-weight: 600;
}

h4 {
  font-size: 24px;
  line-height: 28px;
  margin-top: -9px;
  font-weight: 600;
}

h5 {
  font-size: 20px;
  line-height: 26px;
  margin-top: -9px;
  font-weight: 600;
}

h6 {
  font-size: 18px;
  margin-top: -7px;
  font-weight: 500;
}

p {
  margin-top: -12px;
}
p:last-child {
  margin-bottom: -7px !important;
}
p a {
  color: #ee4730;
}
p a:hover {
  color: #ee4730;
}
@media (max-width: 575px) {
  p {
    font-size: 16px;
  }
}

@media (min-width: 768px) {
  h1 {
    font-size: 80px;
  }
  h2 {
    font-size: 54px;
    line-height: 64px;
    margin-top: -20px;
  }
  h3 {
    font-size: 36px;
    line-height: 46px;
    margin-top: -16px;
  }
  h4 {
    font-size: 28px;
    line-height: 38px;
    margin-top: -14px;
  }
  h5 {
    font-size: 24px;
    line-height: 28px;
    margin-top: -9px;
  }
  h6 {
    font-size: 20px;
    line-height: 26px;
    margin-top: -9px;
  }
}
a {
  display: inline-block;
  transition: all ease 0.3s;
  text-decoration: none;
}
a:hover {
  color: inherit;
}

ul {
  margin: 0;
  padding: 0;
}
ul li {
  list-style: none;
  padding: 5px 0;
}

select,
input,
textarea,
button {
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #bccaea;
  outline: none;
}

textarea {
  height: 200px;
  resize: none;
  padding: 10px;
}

input,
button,
select {
  height: 50px;
  color: #bdb9f0;
}

label,
button,
select {
  cursor: pointer;
}

input {
  background: #f4f4f4;
  border: 1px solid transparent;
  border-radius: 5px;
  padding-left: 10px;
}
input:focus {
  border: 1px solid #ff8a00;
}
input[type=submit] {
  cursor: pointer;
  background-color: #0a3d62;
  color: #ffffff;
  font-weight: 700;
  padding: 0;
}

.bg_img {
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
}

.bg_auto {
  background-size: auto;
}

.bg_cover {
  background-size: cover !important;
}

.right_center {
  background-position: right center;
}

.left_center {
  background-position: left center;
}

.bottom_center {
  background-position: bottom center;
}

.bottom_left {
  background-position: bottom left;
}

.bottom_right {
  background-position: bottom right;
}

.top_center {
  background-position: top center;
}

.top_left {
  background-position: top left;
}

.top_right {
  background-position: top right;
}

.bg_contain {
  background-size: contain;
}

.pricing-item-20::before, .client-item-2 .client-content .quote, .feature--wrapper-17::before, .amazing-slider::before, .post-item.style-two::before, .post-item.style-two::after, .maps-wrapper::before, .map-section::before, .contact-content .section-header a::before, .team-con-wrapper::before, .history-item .history-content::after, .history-item .history-content::before, .team-item .team-thumb::after, .team-item .team-thumb::before, .counter-area-5::after, .counter-area-5::before, .pricing-item-4::before, .pricing-item-4::after, .pricing-item-4 .pricing-header::after, .about-feature-counter-area::after, .about-feature-counter-area::before, .recharge-item .recharge-thumb .anime::before, .recharge-item .recharge-thumb .anime::after, .addon-wrapper .item, .work-slider .owl-nav [class*=owl-]::before, .to-access-item .to-access-thumb span::before, .to-access-item .to-access-thumb span::after, .how-wrapper::after, .how--item .anime-item::after, .how--item .anime-item::before, .how-main-bg, .feature-item .feature-thumb::after, .feature-item .feature-thumb::before, .invest-range-slider .ui-slider-handle::after, .invest-range-slider .ui-slider-handle::before, .testimonial-wrapper .testimonial-area::after, .testimonial-wrapper .testimonial-area::before, .app-bg, .banner-shape-9, .banner-bg-6, .banner-2 .banner-bg-2, .client-item-16 .client-content::before, .light-blue-bg::before, .footer-section::before, .footer-link li::after, .bg-xl-100 {
  position: absolute;
  content: "";
}

.client-item-2, .video-tour-item, .amazing-platform-area, .dots-2, .app-nav, .stroke-counter-wrapper, .video-button-2, .banner-button-group, .button-group, .app-button-group, .post-details .tags-area .tags, .post-details .tags-area, .post-details .post-content .entry-meta .thumb, .post-details .post-content .entry-meta, .post-details .post-content, .comment-area li .blog-item, .blog-author, .contact-item, .contact-form .form-group .form-check, .user-range-area, .cola-item, .feature-tab-menu, .error-section .man1, .error-section .man2, .error-section, .account-section, .countdown, .coming-soon, .comunity-wrapper, .review-item .review-thumb .review-author-content, .review-item .review-thumb, .user-counter-wrapper, .partner-item, .progress-area .progress-item .progress-label, .team-con-area .team-icons, .team-con-area .item, .team-con-area, .team-wrapper, .sponsor-slider-4 .sponsor-thumb, .counter-area-5 .counter-item-5, .counter-area-5, .page-header-content .breadcrumb li a, .counter-area-4 .counter-item-4, .counter-area-4, .pricing-wrapper-4, .pricing-item-4 .pricing-header, .about-feature-counter-area, .help-item, .recharge-wrapper, .recharge-item .recharge-thumb, .cost-wrapper .cost-icon .icon, .cost-wrapper .cost-icon, .addon-wrapper .item, .addon-wrapper .addon-center, .ex-item .ex-thumb, .pricing-menu, .count-slider .count-item, .count-slider, .to-access-item .to-access-thumb, .advance-feature-item, .counter-item-2 .title, .counter-wrapper-2, .bal-list, .how--item, .counter--item, .counter-area, .download-area, .feature-video-area .button-area, .feature-item .feature-thumb, .feature-item, .coverage-wrapper div[class*=border-item], .coverage-right-area .rating-area, .range-wrapper-2 .pricing-range .pricing-range-bottom, .download-options, .amount-area, .colaboration-item, .exclusive-item, .trigger, .faq-item, .trial-wrapper, .widget-post .slider-nav, .widget-post .widget-slider .item .content .meta-post, .widget-categories ul li a, .widget-tags ul, .sponsor-slider-wrapper .sponsor-thumb, .banner-12 .banner-odometer .counter-item, .banner-12 .banner-odometer, .banner-odometer-two .counter-item, .banner-odometer-two, .counter-wrapper-3 .counter-item, .counter-wrapper-3, .banner-1-slider-wrapper .ban-click, .banner-nav-container .ban-nav, .banner-4 .banner-odometer .counter-item, .banner-4 .banner-odometer, .header-section .header-wrapper, .client-say-16, .appreciate-item .header, .app-download-16, .pricing-area-16, .footer-link, .social-icons {
  display: flex;
  flex-wrap: wrap;
}

.apps-download-screen-20 .apps-download-thumb, .dots-2 .owl-dot span, .video-button-2 span::after, .video-button-2 span::before, .video-button-2 span, .video-button-2, .video-button::after, .video-button::before, .post-details .post-content .entry-meta .thumb::before, .user-counter-wrapper .user-counter-bg, .anime-item-2::after, .anime-item-2::before, .anime-item-2, .page-header.single-header::before, .pricing-item-4 .pricing-header::before, .recharge-item .recharge-thumb .anime, .addon-wrapper .addon-center::after, .addon-wrapper .addon-center::before, .addon-wrapper .addon-center, .addon-wrapper::before, .ex-item::before, .count-slider .count-item .serial, .count-slider .count-item::after, .to-access-item .to-access-thumb span, .member-counter-area .member-bg, .how--item .anime-item, .how--item .how-content .serial, .custom-wrapper .calculate-bg, .custom-wrapper .circle::after, .feature-video-area .button-area, .feature-item::after, .feature-item::before, .banner-video-14 .video-button, .extra-bg, .banner-9-video .video-button, .banner-5 .banner-bg-5 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  content: "";
}

.padding-top {
  padding-top: 80px;
}

.padding-bottom {
  padding-bottom: 80px;
}

.padding-top-2 {
  padding-top: 40px;
}

.padding-bottom-2 {
  padding-bottom: 40px;
}

@media (min-width: 768px) {
  .padding-top {
    padding-top: 100px;
  }
  .padding-bottom {
    padding-bottom: 100px;
  }
  .padding-top-2 {
    padding-top: 50px;
  }
  .padding-bottom-2 {
    padding-bottom: 50px;
  }
}
@media (min-width: 992px) {
  .padding-top {
    padding-top: 120px;
  }
  .padding-bottom {
    padding-bottom: 120px;
  }
  .padding-top-2 {
    padding-top: 60px;
  }
  .padding-bottom-2 {
    padding-bottom: 60px;
  }
  .pt-lg-120 {
    padding-top: 120px !important;
  }
  .mt-lg-120 {
    margin-top: 120px !important;
  }
  .lg-oh {
    overflow: hidden;
  }
  .pt-lg-half {
    padding-top: 60px !important;
  }
  .pb-lg-half {
    padding-bottom: 60px !important;
  }
  .mt-lg--90 {
    margin-top: -90px;
    position: relative;
    z-index: 9;
  }
}
.pr {
  position: relative;
}

/*Section Header*/
.mb-30-none {
  margin-bottom: -30px !important;
}

.tab-item {
  display: none;
  animation-name: fadeInUp;
  -webkit-animation-name: fadeInUp;
  -moz-animation-name: fadeInUp;
  animation-duration: 1s;
  -webkit-animation-duration: 1s;
  -moz-animation-duration: 1s;
}
.tab-item.active {
  display: block;
}

.tab-menu li {
  cursor: pointer;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb--20 {
  margin-bottom: -20px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb--50 {
  margin-bottom: -50px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-50 {
  padding-bottom: 50px;
}

.mb--38 {
  margin-bottom: -38px;
}

.mb-40-none {
  margin-bottom: -40px;
}

@media (min-width: 1200px) {
  .pr-xl-15 {
    padding-right: 15px;
  }
}
.c-thumb {
  overflow: hidden;
}
.c-thumb a {
  display: block;
}
.c-thumb img {
  width: 100%;
  transition: all ease 0.3s;
}

.cl-white {
  color: #ffffff;
}
.cl-white * {
  color: #ffffff;
}
.cl-white *::-moz-placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.cl-white *::placeholder {
  color: rgba(255, 255, 255, 0.6);
}
.cl-white p {
  color: rgba(255, 255, 255, 0.9);
}

.cl-black {
  color: #bdb9f0;
}
.cl-black h1, .cl-black h2, .cl-black h3, .cl-black h4, .cl-black h5 {
  color: #ffffff;
}

@media (min-width: 992px) {
  .mw-lg-100 {
    min-width: 100%;
  }
}

@media (min-width: 1200px) {
  .mw-xl-100 {
    min-width: 100%;
  }
}

.oh {
  overflow: hidden;
}

/*Section-Header Starts Here*/
.section-bg {
  background: #f8f9fa;
}

.body-bg {
  background: #202342;
}

.theme-bg {
  background: #ee4730;
}

.theme-one {
  background: #ff8a00;
}

.theme-two {
  background: #05c3de;
}

.theme-six {
  background: #31377d;
}

.mw-100 {
  max-width: 100%;
}

.overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.7);
  transition: all ease 0.3s;
  visibility: hidden;
  opacity: 0;
}
.overlay.active {
  opacity: 1;
  visibility: visible;
}

/*Social Icons*/
.social-icons {
  justify-content: center;
  margin: -7.5px 0;
}
.social-icons li {
  padding: 7.5px;
}
.social-icons li a {
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 16px;
  border: 1px solid #4c218b;
  border-radius: 50%;
  color: #ffffff;
}
.social-icons li a.active, .social-icons li a:hover {
  background: #ee4730;
  border-color: #ee4730;
}

.section-header {
  text-align: center;
  margin: 0 auto 40px;
  max-width: 620px;
  position: relative;
  z-index: 1;
}
.section-header .cate {
  margin-bottom: 38px;
  color: #ff8a00;
}
@media (max-width: 767px) {
  .section-header .cate {
    margin-bottom: 25px;
  }
}
.section-header .title {
  margin-bottom: 18px;
  font-weight: 700;
}
.section-header .title:last-child {
  margin-bottom: -19px;
}
@media (min-width: 768px) {
  .section-header .title {
    margin-bottom: 24px;
  }
}
.section-header p i {
  color: #ee4730;
}
.section-header .thumb {
  width: 100px;
  text-align: center;
  margin: 0 auto 30px;
}
.section-header .thumb img {
  margin: 0 auto;
  max-width: 100%;
}
@media (min-width: 768px) {
  .section-header {
    margin-bottom: 60px;
  }
  .section-header .thumb {
    width: 175px;
    margin-bottom: 53px;
  }
  .section-header.mb-olpo {
    margin-bottom: 45px;
  }
}
.section-header.left-style {
  text-align: left;
  margin-left: 0;
  max-width: 100%;
}

.mw-725 {
  max-width: 725px;
}

.mw-500 {
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.ratings span {
  color: #ffcc00;
}

.rtl {
  direction: rtl;
}

.ltr {
  direction: ltr;
}

.select-bar .current {
  display: block;
  margin-top: 2px;
}
.select-bar .list {
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
}
.select-bar .list::-webkit-scrollbar {
  width: 4px;
  background-color: #F5F5F5;
}
.select-bar .list::-webkit-scrollbar-thumb {
  background-color: #000000;
}
.select-bar .list::-webkit-scrollbar-track {
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.3);
  background-color: #F5F5F5;
}
.select-bar .list li {
  padding: 0 20px;
}

.nice-select:after {
  width: 10px;
  height: 10px;
}

.pos-rel {
  position: relative;
}
.pos-rel .container {
  position: relative;
  z-index: 1;
}

@media (max-width: 991px) {
  .white-lg-black {
    color: rgba(255, 255, 255, 0.9);
  }
  .white-lg-black .title {
    color: #ffffff;
  }
}

@media (max-width: 1199px) {
  .white-xl-black {
    color: rgba(255, 255, 255, 0.9);
  }
  .white-xl-black .title {
    color: #ffffff;
  }
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb--50 {
  margin-bottom: -50px;
}

@media (max-width: 991px) {
  .pb-max-lg-0 {
    padding-bottom: 0 !important;
  }
  .pt-max-lg-0 {
    padding-top: 0 !important;
  }
  .mt-max-lg-0 {
    margin-top: 0 !important;
  }
  .mb-max-lg-0 {
    margin-bottom: 0 !important;
  }
  .bg-max-lg-dark {
    background: #31377d !important;
  }
}
@media (max-width: 767px) {
  .pb-max-md-0 {
    padding-bottom: 0 !important;
  }
  .pt-max-md-0 {
    padding-top: 0 !important;
  }
  .mt-max-md-0 {
    margin-top: 0 !important;
  }
  .mb-max-md-0 {
    margin-bottom: 0 !important;
  }
  .bg-max-md-dark {
    background: #31377d !important;
  }
}
.ash-gradient-bg {
  background: linear-gradient(-89deg, rgb(237, 242, 253) 12%, rgb(203, 217, 245) 100%);
}

@media (min-width: 768px) {
  .mb-md-95 {
    margin-bottom: 95px;
  }
}
@media (max-width: 1199px) {
  .mt-max-xl-0 {
    margin-top: 0;
  }
}
.bg-xl-100 {
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
}
@media (min-width: 1200px) {
  .bg-xl-100 {
    bottom: 160px;
  }
}

.dark-bg {
  background: #3b368c;
}

.section-bg {
  background: #f8f9fa;
}

.body-bg {
  background: #202342;
}

@media (min-width: 992px) {
  .footer-top {
    padding-bottom: 112px;
  }
}
.footer-top .logo {
  max-width: 140px;
  margin: 0 auto 30px;
}
.footer-top .logo a {
  display: block;
}
.footer-top .logo a img {
  width: 100%;
}

.footer-bottom {
  overflow: hidden;
  padding-bottom: 27px;
}

.footer-link {
  justify-content: center;
  margin: -5px -20px;
}
.footer-link li {
  padding: 5px 20px;
  position: relative;
}
.footer-link li a {
  font-size: 16px;
  color: #ffffff;
}
.footer-link li a:hover {
  color: #ee4730;
}
@media (max-width: 575px) {
  .footer-link li a {
    font-size: 14px;
  }
}
.footer-link li::after {
  width: 2px;
  height: 19px;
  background: #5e5bb7;
  right: 0;
  top: 11px;
}
.footer-link li:last-child::after {
  display: none;
}
@media (min-width: 992px) {
  .footer-link {
    margin: -5px -40px;
  }
  .footer-link li {
    padding: 5px 40px;
  }
}

.copyright {
  text-align: center;
  color: #ffffff;
  padding: 22px 0;
  border-top: 1px solid #49329b;
}
.copyright p {
  margin: 0 !important;
}

.footer-section {
  position: relative;
  overflow: hidden;
}
.footer-section::before {
  top: -3px;
  left: 0;
  width: 100%;
  height: 180px;
  background: url(img/footer-shape.png) no-repeat center bottom;
  background-size: cover;
}
@media (min-width: 1200px) {
  .footer-section::before {
    height: 210px;
  }
}
@media (max-width: 767px) {
  .footer-section::before {
    display: none;
  }
}
.footer-section .container {
  position: relative;
  z-index: 1;
}

/*Update Home Page CSS is Here*/
.banner-16 {
  padding: 180px 0 0;
}
@media (min-width: 1200px) {
  .banner-16 {
    padding: 195px 0 0;
    margin-bottom: 50px;
  }
}

.banner-slider-area {
  width: 100%;
  max-width: 355px;
  margin: 160px auto 0;
  position: relative;
}
.banner-slider-area .show-up {
  opacity: 0;
}
.banner-slider-area .show-up img {
  width: 100%;
}
@media (max-width: 1199px) {
  .banner-slider-area {
    margin-top: 80px;
  }
}
@media (max-width: 767px) {
  .banner-slider-area {
    margin-top: 50px;
  }
}

.mobile-slider-16 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  padding: 4px;
  overflow: hidden;
}
@media (max-width: 1199px) {
  .mobile-slider-16 {
    padding: 7px;
  }
}
.mobile-slider-16::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: url(img/frame.png) no-repeat center center;
  background-size: contain;
}
.mobile-slider-16 .mobile-item,
.mobile-slider-16 .owl-item,
.mobile-slider-16 .owl-stage,
.mobile-slider-16 .owl-stage-outer {
  width: 100%;
  height: 100%;
}
.mobile-slider-16 .owl-stage-outer {
  border-radius: 50px;
}
@media screen and (max-width: 1300px) {
  .mobile-slider-16 .owl-stage-outer {
    border-radius: 26px;
  }
}
@media (max-width: 1199px) {
  .mobile-slider-16 .owl-stage-outer {
    border-radius: 22px;
  }
}
.mobile-slider-16 .owl-dots {
  position: absolute;
  bottom: 40px;
  left: 0;
  width: 100%;
  z-index: 98;
}
.mobile-slider-16 .owl-dots .owl-dot span {
  margin: 3px;
  background: #ffffff;
  width: 20px;
  height: 5px;
  display: block;
}
.mobile-slider-16 .owl-dots .owl-dot.active span {
  background: #ee4730;
}

.logo-slider .item {
  text-align: center;
}
.logo-slider .item img {
  max-width: 100%;
  width: unset;
}

.pricing-menu.cl-black li {
  color: #bdb9f0;
  font-family: "Josefin Sans", sans-serif;
}
.pricing-menu.cl-black li::before {
  background: linear-gradient(24deg, rgb(64, 80, 233) 0%, rgb(161, 47, 254) 100%);
}

.pricing-area-16 {
  align-items: center;
  justify-content: space-between;
}

.pricing-item-16 {
  text-align: center;
  font-family: "Josefin Sans", sans-serif;
  padding: 40px 15px;
  border: 1px solid rgba(102, 124, 216, 0.2);
  border-radius: 30px;
  transition: all ease 0.3s;
  position: relative;
  margin-bottom: 30px;
  width: calc((100% - 370px) / 2);
}
.pricing-item-16:nth-of-type(3n + 1) {
  border-right: none;
}
.pricing-item-16:nth-of-type(3n + 3) {
  border-left: none;
}
.pricing-item-16 .pricing-header {
  margin-bottom: 40px;
}
.pricing-item-16 .pricing-header .icon {
  margin-bottom: 27px;
}
.pricing-item-16 .pricing-header .icon img {
  max-width: 100%;
}
.pricing-item-16 ul {
  margin-bottom: 55px;
}
.pricing-item-16 ul li {
  padding: 0;
  color: #ffffff;
  margin-bottom: 12px;
}
.pricing-item-16 .title {
  font-weight: 500;
  color: #ff8a00;
  margin-bottom: 28px;
  line-height: 1;
}
@media (min-width: 768px) {
  .pricing-item-16 .title {
    font-size: 46px;
  }
}
.pricing-item-16 .button-3 {
  border-color: transparent;
  text-transform: capitalize;
}
.pricing-item-16.active, .pricing-item-16:hover {
  background: linear-gradient(90deg, rgb(64, 80, 233) 0%, rgb(161, 47, 254) 100%);
  animation: fadeIn 0.3s;
  -webkit-animation: fadeIn 0.3s;
  -moz-animation: fadeIn 0.3s;
}
.pricing-item-16.active ul li, .pricing-item-16.active .subtitle, .pricing-item-16.active .button-3, .pricing-item-16:hover ul li, .pricing-item-16:hover .subtitle, .pricing-item-16:hover .button-3 {
  color: #ffffff !important;
}
.pricing-item-16.active {
  padding: 80px 15px;
  width: 350px;
}
@media (max-width: 1199px) {
  .pricing-item-16 {
    width: calc((100% - 20px) / 3) !important;
  }
}
@media (max-width: 991px) {
  .pricing-item-16 {
    width: 100% !important;
    max-width: 350px;
    margin: 0 auto 30px;
    border: 1px solid rgba(102, 124, 216, 0.2) !important;
  }
}

.price-tags {
  line-height: 46px;
  font-family: "Josefin Sans", sans-serif;
  padding: 0 28px;
  border-radius: 0 0 25px 25px;
  background: #ffffff;
  color: #ffffff;
  text-transform: uppercase;
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.customize-area {
  text-align: center;
}
.customize-area .title {
  margin-bottom: 15px;
}
@media (min-width: 576px) {
  .customize-area .title {
    margin-bottom: 25px;
  }
}
.customize-area .subtitle a {
  color: #2253ff;
  text-decoration: underline;
}

.footer-section-16 {
  border-radius: 25px 25px 0 0;
}

.app-download-16 {
  margin: -5px auto;
  max-width: 385px;
  justify-content: space-between;
}
.app-download-16 li {
  width: 50%;
  padding: 5px;
  max-width: 170px;
}
.app-download-16 li a {
  display: block;
}
.app-download-16 li a img {
  width: 100%;
}

.border-ash {
  border-color: rgba(255, 255, 255, 0.2);
}

.feature-thumb-16 {
  position: relative;
}
.feature-thumb-16 .anime-1, .feature-thumb-16 .anime-2 {
  position: absolute;
}
.feature-thumb-16 .anime-1 img, .feature-thumb-16 .anime-2 img {
  max-width: 100%;
}
.feature-thumb-16 .anime-2 {
  left: 0;
  top: 7%;
  animation: noraChora 2s linear infinite;
  -webkit-animation: noraChora 2s linear infinite;
  -moz-animation: noraChora 2s linear infinite;
}
@media (max-width: 575px) {
  .feature-thumb-16 .anime-2 {
    width: 230px;
    left: 0;
  }
}
@media screen and (max-width: 575px) and (min-width: 450px) {
  .feature-thumb-16 .anime-2 {
    width: 325px;
  }
}
.feature-thumb-16 .anime-1 {
  right: 0;
  bottom: -25px;
  direction: rtl;
  animation: uthaNama 2s linear infinite;
  -webkit-animation: uthaNama 2s linear infinite;
  -moz-animation: uthaNama 2s linear infinite;
}
@media (min-width: 1200px) {
  .feature-thumb-16 .anime-1.anime-3 {
    bottom: 18%;
  }
}
@media (max-width: 991px) {
  .feature-thumb-16 .anime-1.anime-3 {
    bottom: 18%;
  }
}
@media (max-width: 575px) {
  .feature-thumb-16 .anime-1 {
    width: 230px;
  }
}
@media screen and (max-width: 575px) and (min-width: 450px) {
  .feature-thumb-16 .anime-1 {
    width: 325px;
  }
}

@keyframes uthaNama {
  0% {
    transform: translateY(-10px);
  }
  50% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(-10px);
  }
}
@keyframes noraChora {
  0% {
    transform: translateX(-10px);
  }
  50% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(-10px);
  }
}
.client-wrapper-16 {
  padding: 120px 0 60px;
  border-radius: 20px;
  background: linear-gradient(-90deg, rgb(64, 80, 233) 0%, rgb(161, 47, 254) 100%);
}
@media (max-width: 1199px) {
  .client-wrapper-16 {
    padding: 120px 30px 60px;
  }
}
@media (max-width: 991px) {
  .client-wrapper-16 {
    padding: 80px 15px 40px;
  }
}

.appreciate-item {
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.102);
  box-shadow: 0px 16px 43px 0px rgba(60, 49, 172, 0.19);
  padding: 40px;
  margin-bottom: 30px;
}
.appreciate-item .header {
  align-items: center;
  justify-content: space-between;
  margin: 0 -10px 5px;
}
.appreciate-item .header .title, .appreciate-item .header .ratings {
  margin: 0 10px 10px;
}
.appreciate-item .header .title {
  font-weight: 600;
}
@media screen and (max-width: 399px) {
  .appreciate-item {
    padding: 30px 15px;
  }
}

.ff-heading {
  font-family: "Josefin Sans", sans-serif;
}

.light-blue-bg {
  position: relative;
}
.light-blue-bg .container, .light-blue-bg .container-fluid {
  position: relative;
  z-index: 1;
}
.light-blue-bg::before {
  left: 0;
  right: 0;
  bottom: 0;
  top: 270px;
  background: linear-gradient(90deg, #202342 0%, #31377d 100%);
}

.client-say-16 {
  position: relative;
  justify-content: space-between;
  margin: 50px -15px -30px;
}
@media (min-width: 992px) {
  .client-say-16 {
    margin: 0;
    height: 785px;
  }
  .client-say-16::before {
    position: absolute;
    content: "";
    left: -42px;
    right: -42px;
    bottom: 0;
    top: 0;
    background: url(img/map.png) no-repeat center center;
  }
  .client-say-16 .client-content {
    transition: all ease 0.3s;
    transform: translateY(30px);
    visibility: hidden;
    opacity: 0;
  }
  .client-say-16 .client-content.active {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
  }
  .client-say-16 .client-item-16 {
    position: absolute;
    width: 445px;
  }
  .client-say-16 .client-item-16.active {
    z-index: 9;
  }
  .client-say-16 .client-item-16:nth-child(6) {
    left: 200px;
    bottom: calc(100% - 135px);
  }
  .client-say-16 .client-item-16:nth-child(5) {
    left: 330px;
    bottom: 330px;
  }
  .client-say-16 .client-item-16:nth-child(4) {
    top: 70px;
    left: 0;
  }
  .client-say-16 .client-item-16:nth-child(3) {
    bottom: 140px;
    left: 80px;
  }
  .client-say-16 .client-item-16:nth-child(2) {
    right: 0;
    top: 25px;
    z-index: 2;
  }
  .client-say-16 .client-item-16:nth-child(1) {
    right: -120px;
    bottom: 80px;
  }
}
@media screen and (min-width: 992px) and (max-width: 1600px) {
  .client-say-16 .client-item-16 .client-content {
    box-shadow: 0px 0px 30px rgba(60, 49, 172, 0.2);
  }
}
@media screen and (min-width: 992px) and (max-width: 1023px) {
  .client-say-16 .client-item-16 .client-content {
    box-shadow: none;
  }
}
@media (min-width: 992px) and (max-width: 1399px) {
  .client-say-16 .client-item-16:nth-child(6) {
    left: 200px;
    bottom: calc(100% - 135px);
  }
  .client-say-16 .client-item-16:nth-child(5) {
    left: 330px;
    bottom: 330px;
  }
  .client-say-16 .client-item-16:nth-child(4) {
    top: 70px;
    left: 0;
  }
  .client-say-16 .client-item-16:nth-child(3) {
    bottom: 140px;
    left: 80px;
  }
  .client-say-16 .client-item-16:nth-child(2) {
    right: 0;
    top: 25px;
  }
  .client-say-16 .client-item-16:nth-child(1) {
    right: 0;
    bottom: 80px;
  }
  .client-say-16 .client-item-16:nth-child(1) .client-thumb {
    transform: translateX(120px);
  }
}

.client-item-16 {
  position: relative;
  z-index: 1;
  text-align: center;
  margin: 0 15px 30px;
  width: 100%;
}
.client-item-16 .client-thumb {
  margin: 20px auto 0;
  width: 87px;
  height: 87px;
  border-radius: 50%;
  overflow: hidden;
}
.client-item-16 .client-thumb a {
  display: block;
}
.client-item-16 .client-thumb a img {
  width: 100%;
}
.client-item-16 .client-content {
  border-radius: 20px;
  background-color: #202342;
  box-shadow: 0px 16px 43px 0px rgba(60, 49, 172, 0.2);
  position: relative;
}
.client-item-16 .client-content .body-area {
  padding: 30px 30px 34px;
  border-bottom: 1px solid rgb(218, 185, 250);
}
.client-item-16 .client-content .body-area .head {
  color: #ee4730;
}
.client-item-16 .client-content .body-area p {
  margin-top: 10px;
  color: #ffffff;
}
.client-item-16 .client-content .heading-area {
  padding: 15px 15px 10px;
}
.client-item-16 .client-content .heading-area .title {
  margin: 0;
}
.client-item-16 .client-content .heading-area .info {
  font-size: 14px;
}
.client-item-16 .client-content * {
  position: relative;
  z-index: 1;
}
.client-item-16 .client-content::before {
  width: 80px;
  height: 80px;
  background: #202342;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%) rotate(45deg);
}
@media (min-width: 768px) {
  .client-item-16 {
    width: calc(50% - 30px);
  }
}

/*Header Section Starts Here*/
.header-section {
  width: 100%;
  position: fixed;
  z-index: 999;
  top: 10px;
  transition: all ease 0.3s;
  padding: 15px 0;
  color: #3b368c;
}
.header-section .header-wrapper {
  justify-content: space-between;
  align-items: center;
}
.header-section .header-wrapper .logo {
  width: 140px;
}
.header-section .header-wrapper .logo a {
  display: block;
}
.header-section .header-wrapper .logo a img {
  width: 100%;
}
@media (min-width: 992px) {
  .header-section .header-wrapper .menu {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-left: auto;
    margin-right: 45px;
  }
  .header-section .header-wrapper .menu li {
    padding: 5px;
    position: relative;
  }
  .header-section .header-wrapper .menu li a {
    font-weight: 600;
    text-transform: capitalize;
    color: #ffffff;
    padding: 7px 10px;
  }
  .header-section .header-wrapper .menu li .submenu {
    min-width: 200px;
    background: #3b368c;
    box-shadow: 0 0 5px rgba(136, 136, 136, 0.1);
    display: block;
    position: absolute;
    top: 100%;
    left: 0;
    transform: scaleY(0);
    transform-origin: top;
    z-index: 999;
    transition: all 0.3s ease;
  }
  .header-section .header-wrapper .menu li .submenu li {
    padding: 0;
  }
  .header-section .header-wrapper .menu li .submenu li a {
    text-transform: capitalize;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
    padding: 6px 15px;
    color: #ffffff;
    width: 100%;
    font-weight: 400;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
  .header-section .header-wrapper .menu li .submenu li a:hover, .header-section .header-wrapper .menu li .submenu li a.active {
    color: #ee4730;
    padding-left: 20px;
    background: #ffffff;
  }
  .header-section .header-wrapper .menu li .submenu li:last-child > a {
    border: none;
  }
  .header-section .header-wrapper .menu li .submenu li .submenu {
    left: 100%;
    top: 0;
    transform: scaleX(0);
    transform-origin: left;
  }
  .header-section .header-wrapper .menu li .submenu li:hover > .submenu {
    transform: scaleX(1);
  }
  .header-section .header-wrapper .menu li:hover > .submenu {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .header-section .header-wrapper .menu {
    margin-right: 30px;
  }
  .header-section .header-wrapper .menu li a {
    padding: 5px;
    font-size: 14px;
  }
  .header-section .header-wrapper .menu li .submenu {
    min-width: 180px;
  }
  .header-section .header-wrapper .menu li .submenu li a {
    font-size: 14px;
    padding: 6px 20px;
  }
}
@media (max-width: 991px) {
  .header-section .header-wrapper .menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: calc(100vh - 130px);
    z-index: 9;
    background-color: #3b368c;
    overflow-y: auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.08);
    transition: all ease 0.3s;
    transform: scaleY(0);
    transform-origin: top;
  }
  .header-section .header-wrapper .menu li {
    width: 100%;
    padding: 0;
  }
  .header-section .header-wrapper .menu li a {
    padding: 5px 10px;
    display: flex;
    align-items: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-weight: 500;
    text-transform: capitalize;
    justify-content: space-between;
    color: #ffffff;
  }
  .header-section .header-wrapper .menu li a:hover {
    background: rgba(59, 54, 140, 0.8);
    color: #ffffff;
  }
  .header-section .header-wrapper .menu li a.header-button {
    display: inline-flex;
    padding: 10px 50px;
    background: #ffffff;
    border: none;
    color: #3b368c;
    margin-bottom: 15px !important;
    margin-top: 15px !important;
  }
  .header-section .header-wrapper .menu li .submenu {
    display: none;
    padding-left: 20px;
  }
  .header-section .header-wrapper .menu li .submenu li a {
    font-size: 16px;
  }
  .header-section .header-wrapper .menu.active {
    transform: scaleY(1);
  }
}
.header-section .header-wrapper .select-bar {
  border: none;
  background-color: transparent;
}
.header-section .header-wrapper .select-bar::after {
  border-color: #665cb2;
  width: 8px;
  height: 8px;
}
.header-section .header-wrapper .select-bar .current {
  color: #ffffff;
  text-transform: uppercase;
}
.header-section .header-wrapper .header-button {
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 11px 34px;
  color: #ffffff;
  border: 1px solid #665cb2;
  border-radius: 25px;
  margin-left: 22px;
}
.header-section .header-wrapper .header-button:hover {
  background: #ffffff;
  color: #ee4730;
}
@media (max-width: 1199px) {
  .header-section .header-wrapper .header-button {
    padding: 7px 25px;
    font-size: 14px;
    margin-left: 12px;
  }
}
.header-section.inner-header .header-button {
  border-color: #bc66ef;
}
.header-section.inner-header.active .header-button {
  border-color: #665cb2;
}
.header-section.active {
  background: #3b319e;
  top: 0;
}
@media (max-width: 991px) {
  .header-section.active {
    background: #3b319e;
  }
}
.header-section.active .menu li a.active {
  color: #ee4730 !important;
}
.header-section.header-cl-black.active {
  background-color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 -2px 5px rgba(255, 255, 255, 0.1);
}
.header-section.header-cl-black .header-wrapper .header-bar span {
  background: #ffffff;
}
.header-section.header-cl-black .header-wrapper .select-bar .current {
  color: #ffffff;
}
@media (min-width: 576px) {
  .header-section.header-cl-black .header-wrapper .header-button {
    color: #ffffff;
  }
}
@media (min-width: 992px) {
  .header-section.header-cl-black .header-wrapper .menu li a {
    color: #ffffff;
  }
  .header-section.header-cl-black .header-wrapper .select-bar::after {
    border-color: #3b368c;
  }
}
.header-section.header-white-dark .header-button {
  border-color: #8c69c5;
}
.header-section.header-white-dark .header-button.light {
  border-color: #7d92fc;
}
.header-section.header-white-dark.active {
  background-color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 -2px 5px rgba(255, 255, 255, 0.1);
}
@media (min-width: 576px) {
  .header-section.header-white-dark.active .header-wrapper .header-button {
    color: #3b368c;
  }
  .header-section.header-white-dark.active .header-wrapper .header-button.light {
    border-color: #8c69c5;
  }
}
@media (min-width: 992px) {
  .header-section.header-white-dark.active .header-wrapper .menu li a {
    color: #3b368c;
  }
  .header-section.header-white-dark.active .header-wrapper .menu li ul li a {
    color: #ffffff;
  }
  .header-section.header-white-dark.active .header-wrapper .menu li ul li a:hover {
    color: #ee4730;
  }
}
.header-section.header-white-dark.active .header-wrapper .header-bar span {
  background: #ffffff;
}
.header-section.header-white-dark.active .header-wrapper .select-bar::after {
  border-color: #ffffff;
}
.header-section.header-white-dark.active .header-wrapper .select-bar .current {
  color: #ffffff;
}
@media (max-width: 991px) {
  .header-section.header-white-dark .header-button {
    color: #ffffff;
  }
}
.header-section.header-white-dark .header-wrapper .header-bar span {
  background: #ffffff;
}
.header-section.header-white-dark .header-wrapper .select-bar::after {
  border-color: #ffffff;
}
@media (max-width: 991px) {
  .header-section.header-white-dark .header-wrapper .select-bar .current {
    color: #ffffff;
  }
  .header-section.header-white-dark .header-wrapper .select-bar::after {
    border-color: #ffffff;
  }
}
.header-section .header-right {
  align-items: center;
}
@media (min-width: 992px) {
  .header-section.header-logo-change.active .logo {
    display: none !important;
  }
  .header-section.header-logo-change.active .logo.d-lg-none {
    display: block !important;
  }
}

.menu-item-has-children > a::after {
  content: "\f107";
  font-weight: 600;
  font-family: "Font Awesome 5 Free";
  margin-left: 5px;
}

/*Header Bar Starts*/
.header-bar {
  position: relative;
  cursor: pointer;
  width: 25px;
  height: 20px;
  margin-left: auto;
}
.header-bar span {
  position: absolute;
  display: inline-block;
  height: 3px;
  width: 100%;
  transition: all ease 0.3s;
  background-color: #ffffff;
  left: 0;
}
.header-bar span:first-child {
  top: 0;
}
.header-bar span:nth-child(2) {
  top: 52%;
  transform: translateY(-65%);
}
.header-bar span:last-child {
  bottom: 0;
}
.header-bar.active span:first-child {
  transform: rotate(45deg) translate(3px, 9px);
}
.header-bar.active span:nth-child(2) {
  opacity: 0;
}
.header-bar.active span:last-child {
  transform: rotate(-45deg) translate(3px, -9px);
}

/*Banner Section Three Starts Here*/
.banner-3 {
  padding: 100px 0 47px;
}
@media (max-width: 991px) {
  .banner-3 {
    padding: 200px 0 150px;
  }
}

.banner-content-3 .banner-button-group .play-button {
  color: #ffffff;
}
.banner-content-3 .banner-button-group .play-button img {
  width: 60px;
}
.banner-content-3 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-3 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 33px;
}
@media (max-width: 767px) {
  .banner-content-3 .title {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 10px;
  }
  .banner-content-3 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-3 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-3 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-3 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-3 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-3 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-3 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

@media (min-width: 992px) {
  .banner-thumb-3 {
    transform: translateX(-140px);
  }
}

/*Banner Four Section Starts*/
.banner-4 {
  padding: 115px 0 47px;
}
@media (max-width: 991px) {
  .banner-4 {
    padding: 200px 0 150px;
  }
}
.banner-4 .banner-odometer {
  justify-content: space-between;
  width: 100%;
  max-width: 605px;
  margin-top: 50px;
}
@media screen and (min-width: 500px) {
  .banner-4 .banner-odometer {
    position: relative;
  }
  .banner-4 .banner-odometer::after {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    left: 50%;
    top: 0;
    background: rgba(255, 255, 255, 0.4);
  }
}
@media (min-width: 992px) {
  .banner-4 .banner-odometer {
    margin-top: -115px;
  }
}
.banner-4 .banner-odometer .counter-item {
  max-width: 50%;
}
.banner-4 .banner-odometer .counter-item .counter-thumb {
  width: 65px;
}
.banner-4 .banner-odometer .counter-item .counter-thumb img {
  width: 100%;
}
.banner-4 .banner-odometer .counter-item .counter-content {
  width: calc(100% - 65px);
  padding-left: 25px;
}
.banner-4 .banner-odometer .counter-item .counter-content .title {
  margin: 0;
  font-weight: 300;
  font-size: 60px;
  line-height: 1;
  display: block;
}
.banner-4 .banner-odometer .counter-item .counter-content .title span {
  background: linear-gradient(-90deg, #e28d6f 0%, #e83b99 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  text-shadow: 0.968px 3.881px 1.92px rgba(232, 58, 153, 0.31);
}
.banner-4 .banner-odometer .counter-item:nth-child(even) .title span {
  background: linear-gradient(-90deg, #de3792 0%, #7f6be6 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
@media (max-width: 767px) {
  .banner-4 .banner-odometer .counter-item .counter-content .title {
    font-size: 40px;
  }
}
@media screen and (max-width: 499px) {
  .banner-4 .banner-odometer .counter-item {
    width: 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .banner-4 .banner-odometer .counter-item .counter-content .title {
    font-size: 40px;
  }
  .banner-4 .banner-odometer .counter-item:last-child {
    margin-bottom: 0;
  }
}

.banner-nav-container {
  position: relative;
}
.banner-nav-container .ban-nav {
  width: 160px;
  font-size: 44px;
  line-height: 1;
  justify-content: space-between;
  z-index: 9;
  position: absolute;
  top: 50%;
  left: 100px;
  transform: translate(-50%, -50%) rotate(90deg);
}
@media (min-width: 576px) and (max-width: 991px) and (min-width: 768px) {
  .banner-nav-container .ban-nav {
    left: 200px;
    font-size: 30px;
  }
}
@media (max-width: 575px) {
  .banner-nav-container .ban-nav {
    font-size: 30px;
    width: 120px;
    margin-top: 30px;
  }
}
@media screen and (max-width: 499px) {
  .banner-nav-container .ban-nav {
    left: 80px;
  }
}
@media screen and (max-width: 449px) {
  .banner-nav-container .ban-nav {
    left: 50px;
  }
}
@media screen and (max-width: 389px) {
  .banner-nav-container .ban-nav {
    left: 30px;
  }
}
@media screen and (max-width: 350px) {
  .banner-nav-container .ban-nav {
    left: 20px;
    width: 100px;
    top: 60%;
  }
}
.banner-nav-container .ban-nav a {
  color: #6358f6;
}
.banner-nav-container .ban-nav a.active, .banner-nav-container .ban-nav a:hover {
  color: #ee4730;
  text-shadow: 1px -3px 3px rgba(238, 71, 48, 0.4);
}
@media (min-width: 992px) {
  .banner-nav-container {
    width: 787px;
    height: 859px;
  }
  .banner-nav-container .banner-4-slider {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 446px;
    height: 862px;
  }
}
.banner-nav-container .banner-4-slider {
  position: relative;
  border-radius: 52px 52px 120px 120px;
  padding: 3px 12px 0;
  overflow: hidden;
}
.banner-nav-container .banner-4-slider::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  background: url(img/slide-container.png) no-repeat center top;
  background-size: contain;
  top: 0;
  left: 0;
}
@media (max-width: 991px) {
  .banner-nav-container .banner-4-slider {
    width: 224px;
    height: 431px;
    margin: 0 auto;
    padding: 3px 6px 0;
    border-radius: 26px 26px 60px 60px;
  }
}
@media (max-width: 991px) {
  .banner-nav-container {
    margin-top: 60px;
  }
}
@media (max-width: 575px) {
  .banner-nav-container {
    margin-top: 40px;
  }
}

.banner-4-slider .slide-item,
.banner-4-slider .owl-item,
.banner-4-slider .owl-stage,
.banner-4-slider .owl-stage-outer {
  width: 100%;
  height: 100%;
}

/*Banner One Section Starts*/
.banner-content-1 .banner-button-group .button-4 {
  padding: 15px 37px;
  font-size: 16px;
}
.banner-content-1 .banner-button-group .button-4.active {
  background: transparent;
  color: #ffffff;
  border: 1px solid #5236a5;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content-1 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 767px) {
  .banner-content-1 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 575px) {
  .banner-content-1 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
.banner-content-1 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-1 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-1 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-1 .title {
    margin-bottom: 10px;
  }
  .banner-content-1 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-1 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-1 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-1 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-1 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-1 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-1 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-1 {
  padding: 185px 0 80px;
  position: relative;
}
@media (min-width: 992px) {
  .banner-1 {
    padding: 225px 0 165px;
  }
}
.banner-1 .container {
  position: relative;
  z-index: 99;
}

.dot-3, .dot-4,
.dot-1, .dot-2 {
  position: absolute;
}

.dot-1 {
  bottom: 60px;
  animation: lefTRight 50s linear infinite;
  -webkit-animation: lefTRight 50s linear infinite;
  -moz-animation: lefTRight 50s linear infinite;
}

.dot-2 {
  top: 60px;
  animation: righTLeft 50s linear infinite;
  -webkit-animation: righTLeft 50s linear infinite;
  -moz-animation: righTLeft 50s linear infinite;
}

.dot-3 {
  top: 20%;
  left: 50%;
  animation: zigZag 50s alternate infinite;
  -webkit-animation: zigZag 50s alternate infinite;
  -moz-animation: zigZag 50s alternate infinite;
}
@media (max-width: 991px) {
  .dot-3 {
    top: 13%;
  }
}

.dot-4 {
  bottom: 20%;
  left: 30%;
  animation: zigZag 50s alternate infinite;
  -webkit-animation: zigZag 50s alternate infinite;
  -moz-animation: zigZag 50s alternate infinite;
}

.banner-1-slider-wrapper {
  position: relative;
}
.banner-1-slider-wrapper .ban-click {
  position: absolute;
  left: 0;
  top: 50%;
  transform: rotate(90deg) translate(-157%, 144%);
  align-items: center;
  z-index: 9;
  cursor: pointer;
}
.banner-1-slider-wrapper .ban-click .thumb {
  width: 30px;
}
.banner-1-slider-wrapper .ban-click .thumb img {
  width: 100%;
}
.banner-1-slider-wrapper .ban-click span {
  display: block;
  width: calc(100% - 30px);
  padding-left: 20px;
  font-weight: 600;
}
.banner-1-slider-wrapper .ban-click.two {
  transform: translate(0);
  top: auto;
  bottom: 70px;
  left: calc(100% + 40px);
}
@media (max-width: 767px) {
  .banner-1-slider-wrapper .ban-click.two {
    left: auto;
    right: 50px;
  }
}
.banner-1-slider-wrapper .arrow {
  position: absolute;
  top: 20%;
  left: 20%;
}
.banner-1-slider-wrapper .banner-1-slider {
  width: 766px;
}
@media (max-width: 1199px) {
  .banner-1-slider-wrapper .banner-1-slider {
    width: 600px;
  }
}
@media (max-width: 1199px) and (max-width: 991px) {
  .banner-1-slider-wrapper .banner-1-slider {
    width: 100%;
    margin: 0 auto 40px;
  }
}
@media (max-width: 1199px) and (max-width: 767px) {
  .banner-1-slider-wrapper .banner-1-slider {
    width: 100%;
    margin: 0 auto 30px;
  }
}
@media (max-width: 991px) {
  .banner-1-slider-wrapper {
    max-width: 400px;
    margin: 40px auto 0;
  }
  .banner-1-slider-wrapper .ban-click {
    top: 65%;
  }
}
@media screen and (max-width: 991px) and (max-width: 499px) {
  .banner-1-slider-wrapper .ban-click {
    top: 85%;
  }
}

.banner-1-shape {
  position: absolute;
  width: 100%;
  bottom: 0;
  left: 0;
}
.banner-1-shape img {
  width: 100%;
}

/*Banner Section Two Starts Here*/
.banner-content-2 .banner-button-group .button-4 {
  padding: 15px 37px;
  font-size: 16px;
}
.banner-content-2 .banner-button-group .button-4.active {
  background: transparent;
  color: #ffffff;
  border: 1px solid #dcdcfa;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content-2 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 767px) {
  .banner-content-2 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 575px) {
  .banner-content-2 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
.banner-content-2 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-2 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-2 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-2 .title {
    margin-bottom: 10px;
  }
  .banner-content-2 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-2 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-2 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-2 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-2 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-2 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-2 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-2 {
  padding: 185px 0 80px;
  position: relative;
}
@media (min-width: 992px) {
  .banner-2 {
    padding: 225px 0 165px;
  }
}
.banner-2 .container {
  position: relative;
  z-index: 99;
}
.banner-2 .banner-bg-2 {
  right: 0;
  bottom: 0;
  height: 100%;
  width: 63%;
  background-position: left bottom;
}
@media (max-width: 991px) {
  .banner-2 .banner-bg-2 {
    display: none;
  }
}
@media (min-width: 1200px) {
  .banner-2 .banner-bg-2 {
    width: 65%;
  }
}
@media (min-width: 1400px) {
  .banner-2 .banner-bg-2 {
    width: 62%;
  }
}
@media screen and (min-width: 1700px) {
  .banner-2 .banner-bg-2 {
    width: 61%;
  }
}
@media screen and (min-width: 1800px) {
  .banner-2 .banner-bg-2 {
    width: 60%;
  }
}
.banner-2 .elem-5 {
  bottom: 83%;
  left: 60%;
  animation: rotate 4s alternate infinite;
}
.banner-2 .elem-4 {
  top: 75%;
  left: 95%;
  animation: rotate 10s alternate infinite;
}
.banner-2 .elem-3 {
  bottom: 83%;
  left: 60%;
  animation: rotate 4s alternate infinite;
}
.banner-2 .elem-7 {
  bottom: 90%;
  left: 70%;
  animation: rotate 4s alternate infinite;
}
.banner-2 .elem-6 {
  top: 60%;
  left: 5%;
  animation: rotate 10s alternate infinite;
}
.banner-2 .elem-1 {
  top: 40%;
  right: 7%;
  left: auto;
  animation: rotate 3s alternate infinite;
}
.banner-2 .elem-2 {
  top: 20%;
  right: 5%;
  left: auto;
  animation: rotate 4s alternate infinite;
}

.top-left {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
}
.top-left img {
  max-width: 100%;
}

/*Banner Section Six Starts Here*/
.banner-content-6 {
  max-width: 750px;
  margin: 0 auto 36px;
  text-align: center;
}
.banner-content-6 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 30px;
}
.banner-content-6 .banner-button-group {
  justify-content: center;
}
@media (max-width: 991px) {
  .banner-content-6 .title {
    font-size: 60px;
    line-height: 72px;
  }
}
@media (max-width: 767px) {
  .banner-content-6 .title {
    font-size: 42px;
    line-height: 54px;
  }
}
@media (max-width: 575px) {
  .banner-content-6 .title {
    font-size: 36px;
    line-height: 44px;
  }
}

.banner-bg-6 {
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: 101%;
  height: 101%;
  min-width: 1920px;
}
.banner-bg-6 img {
  width: 100%;
}

.banner-6 {
  padding: 160px 0 0;
  background: #202342;
}
@media (max-width: 767px) {
  .banner-6 {
    padding: 160px 0 0;
  }
}

/*Banner Section Five Starts Here*/
.banner-5 {
  position: relative;
}
.banner-5 .banner-thumb-5 {
  transform: translateX(-255px);
}
@media (min-width: 992px) {
  .banner-5 {
    padding: 130px 0 90px;
  }
}
.banner-5 .container {
  position: relative;
  z-index: 99;
}
.banner-5 .banner-bg-5 {
  width: 100%;
  height: 100%;
}
.banner-5 .banner-bg-5 img {
  width: 100%;
}
@media (max-width: 991px) {
  .banner-5 {
    background: #31377d;
    padding: 185px 0 140px;
  }
}
@media (max-width: 575px) {
  .banner-5 {
    padding: 145px 0 100px;
  }
}

.banner-shape-5 {
  position: absolute;
  left: calc(50% - 270px);
  right: 0;
  top: 0;
  text-align: right;
  bottom: 250px;
}
.banner-shape-5 img {
  height: 100%;
}
@media screen and (min-width: 1700px) {
  .banner-shape-5 img {
    height: auto;
    width: 100%;
  }
}

.banner-search-form {
  position: relative;
}
.banner-search-form input {
  height: 60px;
  border-radius: 30px;
  border: 1px solid #424475;
  background: transparent;
  padding-left: 35px;
}
.banner-search-form input::-moz-placeholder {
  color: #bdb9f0;
}
.banner-search-form input::placeholder {
  color: #bdb9f0;
}
.banner-search-form button {
  position: absolute;
  right: 0;
  top: 0;
  height: 60px;
  border-radius: 30px;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 19px 29px 0px rgba(19, 40, 137, 0.21);
  width: auto;
  padding: 0 30px;
  color: #ffffff;
  font-weight: 600;
  border-color: #424475;
}
@media screen and (max-width: 400px) {
  .banner-search-form input {
    padding-left: 20px;
    height: 50px;
    border-radius: 25px;
  }
  .banner-search-form button {
    height: 50px;
    padding: 0 20px;
    font-size: 13px;
    border-radius: 25px;
  }
}

.banner-content-5 {
  position: relative;
  z-index: 9;
}
.banner-content-5 .banner-search-form {
  margin-bottom: 37px;
  max-width: 458px;
}
@media (max-width: 575px) {
  .banner-content-5 .banner-search-form {
    margin-bottom: 25px;
  }
}
.banner-content-5 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-5 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-5 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-5 .title {
    margin-bottom: 10px;
  }
  .banner-content-5 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-5 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-5 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-5 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-5 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-5 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-5 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

/*Banner Section Seven Starts Here*/
.banner-content-7 .banner-button-group .button-4 {
  padding: 15px 37px;
  font-size: 16px;
}
.banner-content-7 .banner-button-group .button-4.active {
  background: transparent;
  color: #ffffff;
  border: 1px solid #a4a1d1;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content-7 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 767px) {
  .banner-content-7 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 575px) {
  .banner-content-7 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
.banner-content-7 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-7 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-7 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-7 .title {
    margin-bottom: 10px;
  }
  .banner-content-7 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-7 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-7 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-7 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-7 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-7 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-7 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-7 {
  padding: 270px 0 15px;
}
@media (max-width: 991px) {
  .banner-7 {
    padding: 200px 0 15px;
  }
}
@media (max-width: 767px) {
  .banner-7 {
    padding: 160px 0 15px;
  }
}

.counter-wrapper-3 {
  justify-content: center;
  margin-top: 190px;
}
.counter-wrapper-3 .counter-item {
  width: 33.3333333333%;
  justify-content: center;
  margin-bottom: 30px;
}
.counter-wrapper-3 .counter-item .counter-thumb {
  width: 65px;
}
.counter-wrapper-3 .counter-item .counter-thumb img {
  width: 100%;
}
.counter-wrapper-3 .counter-item .counter-content {
  width: auto;
  max-width: calc(100% - 65px);
  padding-left: 25px;
}
.counter-wrapper-3 .counter-item .counter-content .title {
  margin: 0;
  font-weight: 300;
  font-size: 60px;
  line-height: 1;
  display: block;
}
.counter-wrapper-3 .counter-item .counter-content .title span {
  background: linear-gradient(-90deg, #e28d6f 0%, #e83b99 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  text-shadow: 0.968px 3.881px 1.92px rgba(232, 58, 153, 0.31);
}
.counter-wrapper-3 .counter-item:nth-child(even) .title span {
  background: linear-gradient(-90deg, #de3792 0%, #7f6be6 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
@media (max-width: 991px) {
  .counter-wrapper-3 .counter-item .counter-content .name {
    font-size: 14px;
  }
  .counter-wrapper-3 .counter-item .counter-content .title {
    font-size: 40px;
  }
}
@media (max-width: 767px) {
  .counter-wrapper-3 .counter-item {
    width: 50%;
    max-width: 50%;
  }
  .counter-wrapper-3 .counter-item .counter-content .title {
    font-size: 40px;
  }
}
@media screen and (max-width: 499px) {
  .counter-wrapper-3 .counter-item {
    width: 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .counter-wrapper-3 .counter-item .counter-content {
    width: 100%;
  }
  .counter-wrapper-3 .counter-item .counter-content .title {
    font-size: 40px;
  }
  .counter-wrapper-3 .counter-item:last-child {
    margin-bottom: 0;
  }
}
@media (max-width: 991px) {
  .counter-wrapper-3 {
    margin-top: 120px;
  }
}
@media (max-width: 767px) {
  .counter-wrapper-3 {
    margin-top: 60px;
  }
}

/*Banner Section Eight Starts Here*/
.banner-content-8 .banner-button-group .button-4 {
  padding: 15px 37px;
  font-size: 16px;
}
.banner-content-8 .banner-button-group .button-4.active {
  background: transparent;
  color: #ffffff;
  border: 1px solid #a4a1d1;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content-8 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 767px) {
  .banner-content-8 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 575px) {
  .banner-content-8 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
.banner-content-8 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-8 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-8 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-8 .title {
    margin-bottom: 10px;
  }
  .banner-content-8 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-8 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-8 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-8 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-8 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-8 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-8 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-8 {
  padding: 270px 0 15px;
  position: relative;
}
@media (max-width: 991px) {
  .banner-8 {
    padding: 200px 0 115px;
    background: #31377d;
  }
}
@media (max-width: 767px) {
  .banner-8 {
    padding: 160px 0 100px;
  }
}
@media (min-width: 768px) {
  .banner-8 .counter-wrapper-3 {
    margin-top: 80px;
  }
}
@media (min-width: 992px) {
  .banner-8 .counter-wrapper-3 {
    margin-top: 130px;
  }
}
@media screen and (min-width: 1920px) {
  .banner-8 .counter-wrapper-3 {
    margin-top: 220px;
  }
}

.banner-shape-8 {
  position: absolute;
  left: calc(50% - 200px);
  right: 0;
  top: 0;
  text-align: right;
  bottom: 250px;
}
.banner-shape-8 img {
  height: 100%;
}
@media screen and (min-width: 1700px) {
  .banner-shape-8 img {
    height: auto;
    width: 100%;
  }
}

/*Banner Section Nine Starts Here*/
.banner-9 {
  padding: 130px 0 0;
}

.banner-shape-9 {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 101%;
  z-index: 2;
}
.banner-shape-9 img {
  width: 100%;
}

.banner-content-9 {
  text-align: center;
  max-width: 735px;
  margin: 0 auto 60px;
}
.banner-content-9 .banner-button-group {
  justify-content: center;
}
.banner-content-9 .banner-button-group .button-4 {
  padding: 15px 37px;
  font-size: 16px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content-9 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 767px) {
  .banner-content-9 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 575px) {
  .banner-content-9 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
.banner-content-9 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-9 p {
  max-width: 460px;
  font-size: 24px;
  line-height: 34px;
  margin: 0 auto;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-9 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-9 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-9 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-9 {
    margin-bottom: 50px;
  }
  .banner-content-9 .title {
    font-size: 40px;
    line-height: 50px;
  }
  .banner-content-9 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-9 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-9 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-9 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-9 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-9-video {
  z-index: 3;
  position: relative;
}
.banner-9-video img {
  width: 100%;
  z-index: -1;
}
.banner-9-video .video-button {
  z-index: 9;
}

/*Banner Section 10 Starts Here*/
.banner-content-10 .banner-button-group .button-4 {
  padding: 15px 37px;
  font-size: 16px;
}
.banner-content-10 .banner-button-group .button-4.active {
  background: transparent;
  color: #ffffff;
  border: 1px solid #5236a5;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .banner-content-10 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 767px) {
  .banner-content-10 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
@media (max-width: 575px) {
  .banner-content-10 .banner-button-group .button-4 {
    font-size: 14px;
    padding: 10px 25px;
  }
}
.banner-content-10 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-10 p {
  font-size: 24px;
  line-height: 34px;
  max-width: 455px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-10 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-10 .title {
    margin-bottom: 10px;
  }
  .banner-content-10 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-10 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-10 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-10 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-10 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-10 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-10 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-10 {
  padding: 185px 0 120px;
  position: relative;
}
@media (min-width: 576px) {
  .banner-10 {
    padding: 185px 0 140px;
  }
}
@media (min-width: 992px) {
  .banner-10 {
    padding: 200px 0 190px;
  }
}
@media (min-width: 1400px) {
  .banner-10 {
    padding: 282px 0 190px;
  }
}
.banner-10 .container {
  position: relative;
  z-index: 99;
}

.banner-thumb-10 {
  transform: translateX(-233px);
}

.banner-10-trops {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
.banner-10-trops div[class*=trops] {
  position: absolute;
}
.banner-10-trops .trops-1 {
  top: 70%;
  left: 20%;
  animation: zigZag 50s alternate infinite;
  -webkit-animation: zigZag 50s alternate infinite;
  -moz-animation: zigZag 50s alternate infinite;
}
.banner-10-trops .trops-2 {
  top: 30%;
  left: 50%;
  animation: rotate2 50s alternate infinite;
  -webkit-animation: rotate2 50s alternate infinite;
  -moz-animation: rotate2 50s alternate infinite;
}
.banner-10-trops .trops-3 {
  top: 50%;
  left: 55%;
  animation: rev-rotate 30s alternate infinite;
  -webkit-animation: rev-rotate 30s alternate infinite;
  -moz-animation: rev-rotate 30s alternate infinite;
}
.banner-10-trops .trops-4 {
  top: 20%;
  left: 75%;
  animation: rev-rotate 30s alternate infinite;
  -webkit-animation: rev-rotate 30s alternate infinite;
  -moz-animation: rev-rotate 30s alternate infinite;
}
.banner-10-trops .trops-5 {
  top: 23%;
  left: 85%;
  animation: zigZag2 50s alternate infinite;
  -webkit-animation: zigZag2 50s alternate infinite;
  -moz-animation: zigZag2 50s alternate infinite;
}
.banner-10-trops .trops-6 {
  top: 58%;
  left: 97%;
  animation: zigZag2 20s alternate infinite;
  -webkit-animation: zigZag2 20s alternate infinite;
  -moz-animation: zigZag2 20s alternate infinite;
}
.banner-10-trops .trops-7 {
  top: 40%;
  left: 98%;
  animation: rotate2 50s alternate infinite;
  -webkit-animation: rotate2 50s alternate infinite;
  -moz-animation: rotate2 50s alternate infinite;
}

/*Banner 11 Section Starts Here*/
.extra-bg {
  width: 100%;
  height: 100%;
}
@media (max-width: 991px) {
  .extra-bg {
    background: #31377d !important;
  }
}

@media (min-width: 992px) {
  .banner-11 {
    padding: 210px 0 100px;
  }
}
@media (min-width: 1400px) {
  .banner-11 {
    padding: 310px 0 110px;
  }
}
@media (max-width: 991px) {
  .banner-11 {
    padding: 185px 0 140px;
  }
}
@media (max-width: 575px) {
  .banner-11 {
    padding: 145px 0 100px;
  }
}

.banner-search-form.style-two button {
  position: absolute;
  right: 0;
  top: 0;
  height: 60px;
  width: 60px;
  border-radius: 30px;
  font-weight: 600;
  text-align: center;
  padding: 0;
}
@media screen and (max-width: 400px) {
  .banner-search-form.style-two input {
    padding: 0 80px 0 20px;
    height: 50px;
    border-radius: 25px;
  }
  .banner-search-form.style-two button {
    height: 50px;
    height: 50px;
    border-radius: 25px;
  }
}

.banner-content-11 {
  position: relative;
  z-index: 9;
}
.banner-content-11 .banner-search-form {
  margin-bottom: 37px;
  max-width: 458px;
}
@media (max-width: 575px) {
  .banner-content-11 .banner-search-form {
    margin-bottom: 25px;
  }
}
.banner-content-11 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-11 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-11 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-11 .title {
    margin-bottom: 10px;
  }
  .banner-content-11 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-11 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-11 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-11 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-11 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-11 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-11 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-odometer-two {
  justify-content: space-between;
  width: 100%;
  max-width: 605px;
  margin-top: 50px;
}
@media (min-width: 1200px) {
  .banner-odometer-two {
    margin-left: -20px;
    margin-right: -20px;
    margin-top: 120px;
    justify-content: flex-start;
  }
  .banner-odometer-two .counter-item {
    padding: 0 20px;
    max-width: 50%;
  }
}
.banner-odometer-two .counter-item {
  max-width: 50%;
}
.banner-odometer-two .counter-item .counter-thumb {
  width: 65px;
}
.banner-odometer-two .counter-item .counter-thumb img {
  width: 100%;
}
.banner-odometer-two .counter-item .counter-content {
  width: calc(100% - 65px);
  padding-left: 25px;
}
.banner-odometer-two .counter-item .counter-content .title {
  margin: 0;
  font-weight: 300;
  font-size: 60px;
  line-height: 1;
  display: block;
}
.banner-odometer-two .counter-item .counter-content .title span {
  background: linear-gradient(-90deg, #e28d6f 0%, #e83b99 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  text-shadow: 0.968px 3.881px 1.92px rgba(232, 58, 153, 0.31);
}
.banner-odometer-two .counter-item:nth-child(even) .title span {
  background: linear-gradient(-90deg, #de3792 0%, #7f6be6 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
@media (max-width: 767px) {
  .banner-odometer-two .counter-item .counter-content .title {
    font-size: 40px;
  }
}
@media screen and (max-width: 499px) {
  .banner-odometer-two .counter-item {
    width: 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .banner-odometer-two .counter-item .counter-content .title {
    font-size: 40px;
  }
  .banner-odometer-two .counter-item:last-child {
    margin-bottom: 0;
  }
}

.banner-thumb-11 {
  position: relative;
}
@media (min-width: 1200px) {
  .banner-thumb-11 .main-thumb {
    margin-top: 20px;
    margin-left: -55px;
  }
}
.banner-thumb-11 .boy, .banner-thumb-11 .girl, .banner-thumb-11 .tree1, .banner-thumb-11 .tree2, .banner-thumb-11 .graph {
  position: absolute;
}
.banner-thumb-11 .girl, .banner-thumb-11 .boy {
  bottom: -110px;
}
.banner-thumb-11 .girl {
  left: 250px;
}
.banner-thumb-11 .boy {
  left: 480px;
}
.banner-thumb-11 .tree1 {
  bottom: -85px;
  left: 190px;
}
.banner-thumb-11 .tree2 {
  bottom: -85px;
  left: 805px;
}
.banner-thumb-11 .graph {
  bottom: 263px;
  left: 240px;
}
.banner-thumb-11 .graph img {
  animation: upDown 2s alternate infinite;
  -webkit-animation: upDown 2s alternate infinite;
  -moz-animation: upDown 2s alternate infinite;
}
@media (max-width: 1399px) {
  .banner-thumb-11 .main-thumb {
    width: 800px;
  }
  .banner-thumb-11 img {
    max-width: 100%;
  }
  .banner-thumb-11 .boy {
    width: 200px;
    left: 350px;
  }
  .banner-thumb-11 .graph {
    width: 135px;
    left: 250px;
    bottom: 200px;
  }
  .banner-thumb-11 .girl {
    width: 200px;
    left: 200px;
  }
  .banner-thumb-11 .tree2 {
    width: 60px;
    left: 550px;
  }
  .banner-thumb-11 .tree1 {
    width: 60px;
    left: 150px;
  }
}

/*Banner Section 12 Starts Here*/
@media (min-width: 992px) {
  .banner-content-12 {
    margin-bottom: 50px;
  }
}
.banner-content-12 .banner-button-group .play-button {
  color: #ffffff;
}
.banner-content-12 .banner-button-group .play-button img {
  width: 60px;
}
.banner-content-12 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-12 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 33px;
}
@media (max-width: 767px) {
  .banner-content-12 .title {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 10px;
  }
  .banner-content-12 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-12 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-12 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-12 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-12 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-12 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-12 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}

.banner-12 {
  padding: 115px 0 47px;
}
@media (max-width: 991px) {
  .banner-12 {
    padding: 200px 0 150px;
  }
}
.banner-12 .banner-odometer {
  justify-content: space-between;
  width: 100%;
  max-width: 605px;
  margin-top: 50px;
}
@media screen and (min-width: 500px) {
  .banner-12 .banner-odometer {
    position: relative;
  }
  .banner-12 .banner-odometer::after {
    position: absolute;
    content: "";
    width: 1px;
    height: 100%;
    left: 50%;
    top: 0;
    background: rgba(255, 255, 255, 0.4);
  }
}
@media (min-width: 992px) {
  .banner-12 .banner-odometer {
    margin-top: -115px;
  }
}
.banner-12 .banner-odometer .counter-item {
  max-width: 50%;
}
.banner-12 .banner-odometer .counter-item .counter-thumb {
  width: 65px;
}
.banner-12 .banner-odometer .counter-item .counter-thumb img {
  width: 100%;
}
.banner-12 .banner-odometer .counter-item .counter-content {
  width: calc(100% - 65px);
  padding-left: 25px;
}
.banner-12 .banner-odometer .counter-item .counter-content .title {
  margin: 0;
  font-weight: 300;
  font-size: 60px;
  line-height: 1;
  display: block;
}
.banner-12 .banner-odometer .counter-item .counter-content .title span {
  background: linear-gradient(-90deg, #e28d6f 0%, #e83b99 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  text-shadow: 0.968px 3.881px 1.92px rgba(232, 58, 153, 0.31);
}
.banner-12 .banner-odometer .counter-item:nth-child(even) .title span {
  background: linear-gradient(-90deg, #de3792 0%, #7f6be6 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
@media (max-width: 767px) {
  .banner-12 .banner-odometer .counter-item .counter-content .title {
    font-size: 40px;
  }
}
@media screen and (max-width: 499px) {
  .banner-12 .banner-odometer .counter-item {
    width: 100%;
    max-width: 100%;
    margin-bottom: 30px;
  }
  .banner-12 .banner-odometer .counter-item .counter-content .title {
    font-size: 40px;
  }
  .banner-12 .banner-odometer .counter-item:last-child {
    margin-bottom: 0;
  }
}

/*Banner Section 13 Starts Here*/
.banner-content-13 .banner-button-group .play-button {
  color: #ffffff;
}
.banner-content-13 .banner-button-group .play-button img {
  width: 60px;
}
.banner-content-13 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-13 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 33px;
}
@media (max-width: 767px) {
  .banner-content-13 .title {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 10px;
  }
  .banner-content-13 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-13 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-13 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-13 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-13 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-13 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-13 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}
.banner-content-13 .cate {
  margin-bottom: 25px;
  color: #ff8a00;
}
@media (max-width: 767px) {
  .banner-content-13 .cate {
    margin-bottom: 10px;
  }
}

.banner-13 {
  padding: 300px 0 295px;
}
@media (max-width: 1199px) {
  .banner-13 {
    background: #31377d !important;
    padding: 200px 0 150px;
  }
}
.banner-thumb-13 {
  position: absolute;
  right: calc(50% - 230px);
  bottom: 0;
}

/*banner Section 15 Starts Here*/
.banner-15 {
  padding: 170px 0 98px;
}
@media (max-width: 991px) {
  .banner-15 {
    padding: 200px 0 150px;
  }
}

.banner-content-15 .banner-button-group {
  margin: -15px;
}
.banner-content-15 .banner-button-group a {
  margin: 15px;
}
.banner-content-15 .banner-button-group .play-button {
  color: #ffffff;
}
.banner-content-15 .banner-button-group .play-button img {
  width: 60px;
}
.banner-content-15 .cate {
  margin-bottom: 25px;
  color: #ff8a00;
}
@media (max-width: 767px) {
  .banner-content-15 .cate {
    margin-bottom: 10px;
  }
}
.banner-content-15 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-15 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 33px;
}
@media (max-width: 767px) {
  .banner-content-15 .title {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 10px;
  }
  .banner-content-15 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-15 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-15 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-15 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-15 .banner-button-group a span {
    display: none;
  }
}
@media (min-width: 992px) {
  .banner-content-15 {
    margin-bottom: 120px;
  }
}

/*Banner Section 14 Starts Here*/
.banner-14 {
  padding: 200px 0 120px;
}
@media (min-width: 992px) {
  .banner-14 {
    padding: 245px 0 60px;
  }
}

.banner-content-14 {
  position: relative;
  z-index: 9;
}
.banner-content-14 .banner-search-form {
  margin-bottom: 25px;
  max-width: 458px;
}
@media (max-width: 575px) {
  .banner-content-14 .banner-search-form {
    margin-bottom: 20px;
  }
}
.banner-content-14 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-14 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-14 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 991px) {
  .banner-content-14 {
    margin-bottom: 50px;
  }
}
@media (max-width: 767px) {
  .banner-content-14 {
    margin-bottom: 45px;
  }
  .banner-content-14 .title {
    margin-bottom: 10px;
  }
  .banner-content-14 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-14 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-14 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
  .banner-content-14 .banner-button-group .button-4 {
    font-size: 16px;
    padding: 10px 30px;
  }
  .banner-content-14 .banner-button-group .play-button img {
    width: 40px;
  }
  .banner-content-14 .banner-button-group .play-button i {
    font-size: 40px;
    line-height: 40px;
  }
  .banner-content-14 .banner-button-group .play-button span {
    padding-left: 10px;
  }
}
.banner-content-14 .cate {
  margin-bottom: 25px;
  color: #ff8a00;
}
@media (max-width: 767px) {
  .banner-content-14 .cate {
    margin-bottom: 10px;
  }
}

.banner-video-14 {
  position: relative;
}
@media (max-width: 991px) {
  .banner-video-14 img {
    width: 100%;
  }
}

.sponsor-slider-wrapper {
  margin-top: 60px;
}
@media (min-width: 768px) {
  .sponsor-slider-wrapper {
    margin-top: 80px;
  }
}
.sponsor-slider-wrapper .sponsor-thumb {
  max-width: 100%;
  height: 45px;
  align-items: center;
  justify-content: center;
}
.sponsor-slider-wrapper .sponsor-thumb img {
  max-height: 100%;
  width: auto;
}
.sponsor-slider-wrapper .slider-heading {
  display: block;
  margin-bottom: 22px;
}
@media (min-width: 768px) {
  .sponsor-slider-wrapper .slider-heading {
    margin-bottom: 32px;
  }
}

/*App Banner Starts Here*/
.app-bg {
  bottom: -330px;
  left: 0;
  right: 0;
  top: 0;
}

.app-download-section {
  position: relative;
}

.app-download-content .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.app-download-content p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 58px;
}
.app-download-content .cate {
  margin-bottom: 24px;
}
@media (max-width: 1199px) {
  .app-download-content .cate {
    margin-bottom: 18px;
  }
  .app-download-content .title {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 0;
  }
  .app-download-content p {
    margin-bottom: 40px;
  }
}
@media (max-width: 767px) {
  .app-download-content .title {
    margin-bottom: 10px;
  }
  .app-download-content p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .app-download-content .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .app-download-content p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
}

.app-download {
  padding: 185px 0 80px;
  position: relative;
}
@media (min-width: 992px) {
  .app-download {
    padding: 120px 0 140px;
  }
}
.app-download .container {
  position: relative;
  z-index: 99;
}
.app-download .app-button-group {
  margin-bottom: 40px;
}
.app-download .app-button-group a {
  box-shadow: 0px 19px 21px 0px rgba(41, 17, 168, 0.3);
}
.app-download .joined {
  margin-bottom: 45px;
  display: block;
}
.app-download .amount span {
  color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ffffff;
  text-shadow: 0px 11px 13px rgba(22, 74, 191, 0.3);
}
@media (min-width: 768px) {
  .app-download .amount {
    font-size: 66px;
  }
}
@media (max-width: 1199px) {
  .app-download .app-button-group {
    margin-bottom: 30px;
  }
  .app-download .joined {
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .app-download .app-button-group {
    margin-bottom: 20px;
  }
  .app-download .joined {
    margin-bottom: 20px;
  }
}

/*Blog-Sedebar Starts Here*/
.widget {
  border-radius: 10px;
  background: #31377d;
  padding: 30px;
  margin: 0 auto 30px;
}
@media screen and (max-width: 350px) {
  .widget {
    padding: 30px 15px;
  }
}
.widget:last-child {
  margin-bottom: 0;
}
.widget .title {
  margin-top: 0;
  margin-bottom: 25px;
  font-size: 24px;
  text-transform: capitalize;
  font-weight: 600;
}
.widget-tags ul {
  margin: -5px;
}
.widget-tags ul li {
  padding: 5px;
}
.widget-tags ul li a {
  font-size: 14px;
  text-transform: capitalize;
  color: #bdb9f0;
  padding: 6px 30px;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 20px;
  border: 1px solid #bccaea;
}
.widget-tags ul li a:hover, .widget-tags ul li a.active {
  color: #ffffff;
  border-color: transparent;
  background: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.35);
}
@media screen and (max-width: 380px) {
  .widget-tags ul li a {
    padding: 2px 20px;
  }
}
.widget-categories ul {
  margin-top: -9px;
}
.widget-categories ul li {
  padding: 0;
  border-bottom: 2px dotted #bccaea;
}
.widget-categories ul li a {
  justify-content: space-between;
  color: #bdb9f0;
  padding: 7px 0;
}
.widget-categories ul li a:hover {
  color: #ee4730;
}
.widget-categories ul li:first-child a {
  padding-top: 0;
}
.widget-follow ul {
  margin: -5px;
  justify-content: flex-start;
}
.widget-follow ul li {
  padding: 5px;
}
.widget-follow ul li a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-color: #bccaea;
  color: rgba(189, 185, 240, 0.8);
}
.widget-follow ul li a.active, .widget-follow ul li a:hover {
  border: none;
  color: #ffffff;
  background: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.35);
}
.widget-search .search-form {
  margin-top: -20px;
}
.widget-search input {
  font-size: 14px;
  border-radius: 0;
  padding-left: 0;
  color: #ffffff;
  border: none;
  border-bottom: 1px solid #bccaea;
  background: transparent;
  margin-bottom: 30px;
}
.widget-search input::-moz-placeholder {
  color: #bdb9f0;
}
.widget-search input::placeholder {
  color: #bdb9f0;
}
.widget-search button {
  width: auto;
  min-width: 150px;
  outline: none;
  color: #ffffff;
  height: 40px;
  border-radius: 20px;
  background-image: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.35);
  border: none;
}
.widget-search button i {
  margin-right: 5px;
}
.widget-post {
  position: relative;
}
.widget-post .widget-slider .item .thumb {
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 25px;
}
.widget-post .widget-slider .item .thumb a {
  display: block;
}
.widget-post .widget-slider .item .thumb img {
  width: 100%;
}
.widget-post .widget-slider .item .content .p-title {
  margin-bottom: 7px;
  font-size: 22px;
}
.widget-post .widget-slider .item .content .p-title a {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis;
}
.widget-post .widget-slider .item .content .meta-post {
  font-size: 14px;
}
.widget-post .widget-slider .item .content .meta-post a {
  color: #bdb9f0;
}
.widget-post .widget-slider .item .content .meta-post a i {
  color: #ee4730;
  font-size: 14px;
  margin-right: 5px;
  text-shadow: 0.624px 2.934px 2px rgba(232, 58, 153, 0.3);
}
.widget-post .slider-nav {
  position: absolute;
  right: 30px;
  top: 30px;
  width: 50px;
  justify-content: space-between;
}
.widget-post .slider-nav span {
  display: block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  font-size: 10px;
  box-shadow: 0px 2px 5px 0px rgba(0, 18, 50, 0.2);
  color: #bdb9f0;
  border-radius: 50%;
  transition: all ease 0.3s;
}
.widget-post .slider-nav span:hover, .widget-post .slider-nav span.active {
  color: #ffffff;
  background: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.35);
}

/*Trial Section Starts Here*/
.trial-wrapper {
  align-items: center;
  justify-content: space-between;
  padding-left: 58px;
  padding-right: 58px;
  border-radius: 30px;
  background: linear-gradient(0deg, rgb(92, 104, 242) 12%, rgb(148, 104, 249) 67%, rgb(204, 104, 255) 100%);
}
.trial-wrapper .trial-content {
  width: 50%;
}
.trial-wrapper .trial-content .title {
  max-width: 380px;
  margin-bottom: 27px;
}
.trial-wrapper * {
  position: relative;
  z-index: 1;
}
@media (max-width: 991px) {
  .trial-wrapper .trial-content, .trial-wrapper .trial-button {
    width: 100%;
  }
  .trial-wrapper .trial-content {
    margin-bottom: 45px;
  }
}
@media (max-width: 575px) {
  .trial-wrapper {
    padding-left: 25px;
    padding-right: 25px;
  }
  .trial-wrapper .trial-content {
    margin-bottom: 35px;
  }
  .trial-wrapper .trial-content .title {
    margin-bottom: 20px;
  }
}
.trial-wrapper.style-two {
  border-radius: 30px;
  background-color: rgb(240, 245, 252);
  box-shadow: 4.405px 5.44px 17px 0px rgba(166, 166, 255, 0.31);
}
.trial-wrapper.style-three {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
}

.ball-1 {
  position: absolute;
  bottom: 0;
  right: -30px;
  left: 40px;
  top: -40px;
}
.ball-1 img {
  max-width: 100%;
}
@media (max-width: 1199px) {
  .ball-1 {
    display: none;
  }
}

/*Faq Section Starts Here*/
.faq-header {
  position: sticky;
  top: 100px;
}
.faq-header .cate {
  width: 78px;
  margin-bottom: 45px;
}
.faq-header .cate img {
  width: 100%;
}
@media (max-width: 575px) {
  .faq-header .cate {
    width: 50px;
    margin-bottom: 35px;
  }
}
.faq-header .title {
  margin-bottom: 15px;
}
.faq-header a {
  color: #ffffff;
}
.faq-header a:hover {
  color: #ee4730;
}
.faq-header a i {
  margin-left: 5px;
}
@media (max-width: 991px) {
  .faq-header {
    margin-bottom: 50px;
  }
}
@media (max-width: 575px) {
  .faq-header {
    margin-bottom: 40px;
  }
  .faq-header .title {
    margin-bottom: 7px;
  }
}

.faq-item {
  margin-bottom: 38px;
}
.faq-item .faq-thumb {
  width: 65px;
  height: 65px;
  text-align: center;
  line-height: 65px;
  font-size: 24px;
  font-weight: 600;
  color: #ee4730;
  border: 1px solid rgba(238, 71, 48, 0.2);
  border-radius: 50%;
}
.faq-item .faq-content {
  width: calc(100% - 65px);
  padding-left: 30px;
}
.faq-item .faq-content .title {
  margin-top: 0;
  margin-bottom: 26px;
}
@media (max-width: 575px) {
  .faq-item .faq-thumb {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .faq-item .faq-content {
    width: calc(100% - 40px);
    padding-left: 20px;
  }
  .faq-item .faq-content .title {
    margin-bottom: 20px;
  }
}

/*Testimonial Section Starts Here*/
.testimonial-wrapper {
  border-radius: 20px;
  background: linear-gradient(16deg, rgb(225, 161, 237) 0%, rgb(172, 125, 241) 35%, rgb(118, 88, 244) 100%);
  padding: 80px 80px 110px;
  position: relative;
}
.testimonial-wrapper .testimonial-area {
  max-width: 830px;
  margin: 0 auto;
  position: relative;
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-thumb {
  width: 140px;
  height: 140px;
  border: 1px solid #05c3de;
  border-radius: 50%;
  margin: 0 auto 30px;
  padding: 8px;
}
@media (max-width: 575px) {
  .testimonial-wrapper .testimonial-area .testimonial-item .testimonial-thumb {
    margin-bottom: 20px;
    height: 120px;
    width: 120px;
  }
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-thumb .thumb {
  width: 100%;
  height: 100%;
  border: 7px solid #05c3de;
  border-radius: 50%;
  overflow: hidden;
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-thumb .thumb img {
  width: 100%;
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content {
  padding: 0 30px;
  text-align: center;
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content p {
  color: #ffffff;
  margin-bottom: 20px;
}
@media (min-width: 768px) {
  .testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content p {
    font-size: 24px;
    line-height: 34px;
  }
}
@media (min-width: 992px) {
  .testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content p {
    font-weight: 600;
  }
}
@media (min-width: 576px) {
  .testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content p {
    margin-bottom: 45px;
  }
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content .ratings {
  margin-bottom: 32px;
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content .title {
  font-family: "Open Sans", sans-serif;
  text-transform: capitalize;
  background: rgba(0, 0, 0, 0.102);
  display: inline-block;
  margin: 0;
  padding: 11px 30px;
  border-radius: 25px;
}
.testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content .title a {
  color: #ffffff;
}
@media (max-width: 575px) {
  .testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content .ratings {
    margin-bottom: 20px;
  }
  .testimonial-wrapper .testimonial-area .testimonial-item .testimonial-content .title {
    font-size: 20px;
    font-family: "Josefin Sans", sans-serif;
  }
}
.testimonial-wrapper .testimonial-area::after, .testimonial-wrapper .testimonial-area::before {
  width: 80px;
  height: 60px;
  opacity: 0.1;
}
.testimonial-wrapper .testimonial-area::after {
  background: url(img/before.png) no-repeat right bottom;
  background-size: contain;
  top: 30px;
  left: 0;
}
.testimonial-wrapper .testimonial-area::before {
  background: url(img/after.png) no-repeat left top;
  background-size: contain;
  right: 0;
  bottom: -50px;
}
.testimonial-wrapper .button-area {
  margin-top: 40px;
  text-align: center;
}
@media (min-width: 768px) {
  .testimonial-wrapper .button-area {
    margin-top: 0;
    position: absolute;
    top: 40px;
    right: 40px;
    text-align: right;
  }
}
.testimonial-wrapper .button-area .button-2 {
  text-transform: uppercase;
}
.testimonial-wrapper .trigger {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.testimonial-wrapper .testi-next {
  left: -20px;
}
.testimonial-wrapper .testi-prev {
  right: -20px;
}
@media (min-width: 768px) {
  .testimonial-wrapper.style-two {
    padding: 35px 50px 81px;
    border-radius: 50%;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .testimonial-wrapper.style-two {
    padding: 90px 50px 131px;
    max-width: 630px;
    margin-left: auto;
    margin-right: auto;
    height: 635px;
    width: 635px;
  }
}
@media (min-width: 768px) and (min-width: 1200px) {
  .testimonial-wrapper.style-two {
    padding: 90px 50px 131px;
    height: 635px;
    width: 635px;
  }
}
@media (min-width: 768px) {
  .testimonial-wrapper.style-two .testimonial-area::before {
    right: 20px;
    bottom: -20px;
  }
  .testimonial-wrapper.style-two .testimonial-area::after {
    top: 35px;
    left: 15px;
  }
  .testimonial-wrapper.style-two .testimonial-area .testimonial-item .testimonial-thumb {
    margin-bottom: 20px;
  }
  .testimonial-wrapper.style-two .testimonial-area .testimonial-item .testimonial-content {
    padding: 0;
  }
  .testimonial-wrapper.style-two .testimonial-area .testimonial-item .testimonial-content p {
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
@media (max-width: 767px) {
  .testimonial-wrapper {
    padding: 60px;
  }
}
@media (max-width: 575px) {
  .testimonial-wrapper {
    padding: 50px 0;
  }
  .testimonial-wrapper .trigger {
    display: none;
  }
}
@media screen and (max-width: 575px) and (max-width: 450px) {
  .testimonial-wrapper .testimonial-area::after, .testimonial-wrapper .testimonial-area::before {
    display: none;
  }
}

.trigger {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 5px;
  background: #262f6e;
  align-items: center;
  justify-content: center;
  transition: all ease 0.3s;
}
.trigger img {
  width: 30px;
  margin-top: 4px;
}
.trigger.active, .trigger:hover {
  background: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.5);
}

/*Exclusive Section Starts Here*/
.exclusive-item {
  align-items: center;
  border: 1px solid #312d65;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 20px;
  max-width: 400px;
  transition: all ease 0.3s;
}
.exclusive-item:hover {
  box-shadow: 0px 0px 15px 0px rgba(255, 255, 255, 0.4);
}
.exclusive-item .exclusive-thumb {
  width: 60px;
}
.exclusive-item .exclusive-thumb img {
  width: 100%;
}
.exclusive-item .exclusive-content {
  width: calc(100% - 60px);
  padding-left: 20px;
}
.exclusive-item .exclusive-content .title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-transform: capitalize;
  line-height: 28px;
}

.feature-1-thumb {
  text-align: left;
  margin-top: 170px;
  position: relative;
}
@media screen and (max-width: 1310px) {
  .feature-1-thumb img {
    max-width: 700px;
  }
}
@media screen and (max-width: 1200px) {
  .feature-1-thumb img {
    max-width: 650px;
  }
}
@media screen and (min-width: 1650px) {
  .feature-1-thumb.ex-feature img {
    transform: translateX(80px);
  }
}
.feature-1-thumb .main-thumb {
  transition: all ease 0.5s;
}
.feature-1-thumb .layer {
  transition: all ease 0.5s;
  position: absolute;
  top: 6px;
  left: 6px;
}
.feature-1-thumb .layer img {
  max-width: 904px;
}
@media screen and (max-width: 1310px) {
  .feature-1-thumb .layer {
    top: -3px;
    left: 3px;
  }
  .feature-1-thumb .layer img {
    max-width: 680px;
  }
}
@media screen and (max-width: 1200px) {
  .feature-1-thumb .layer img {
    max-width: 630px;
  }
}
.feature-1-thumb:hover .main-thumb {
  transform: skeW(-9deg, 6deg);
}
.feature-1-thumb:hover .layer:nth-child(5) {
  transform: translate(24px, -160px) skeW(-9deg, 6deg);
}
.feature-1-thumb:hover .layer:nth-child(4) {
  transform: translate(18px, -120px) skeW(-9deg, 6deg);
}
.feature-1-thumb:hover .layer:nth-child(3) {
  transform: translate(12px, -80px) skeW(-9deg, 6deg);
}
.feature-1-thumb:hover .layer:nth-child(2) {
  transform: translate(6px, -40px) skeW(-9deg, 6deg);
}
@media (max-width: 991px) {
  .feature-1-thumb .layer:nth-child(5) {
    transform: translate(24px, -120px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb .layer:nth-child(4) {
    transform: translate(18px, -90px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb .layer:nth-child(3) {
    transform: translate(12px, -60px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb .layer:nth-child(2) {
    transform: translate(6px, -30px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb:hover .layer, .feature-1-thumb:hover .main-thumb {
    transform: translate(0, 0) skeW(0, 0) !important;
  }
}
@media (max-width: 767px) {
  .feature-1-thumb .main-thumb img {
    width: 100%;
  }
  .feature-1-thumb .layer {
    left: 1%;
    top: 1%;
  }
  .feature-1-thumb .layer img {
    width: 98%;
  }
}
@media screen and (max-width: 499px) {
  .feature-1-thumb {
    margin-top: 90px;
  }
  .feature-1-thumb .layer:nth-child(5) {
    transform: translate(12px, -60px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb .layer:nth-child(4) {
    transform: translate(9px, -45px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb .layer:nth-child(3) {
    transform: translate(6px, -30px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb .layer:nth-child(2) {
    transform: translate(3px, -15px) skeW(-9deg, 6deg);
  }
  .feature-1-thumb:hover .layer, .feature-1-thumb:hover .main-thumb {
    transform: translate(0, 0) skeW(0, 0) !important;
  }
}

/*Colaboration Section Starts Here*/
.colaboration-item .colaboration-thumb {
  width: 90px;
  height: 90px;
  padding: 9px;
  border-radius: 50%;
  border: 1px solid #cac7f6;
}
.colaboration-item .colaboration-thumb .icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-image: linear-gradient(0deg, rgb(246, 211, 101) 0%, rgb(253, 160, 133) 100%);
  box-shadow: 2.419px 9.703px 18.24px 0.76px rgba(253, 160, 133, 0.5);
  -webkit-box-shadow: 2.419px 9.703px 18.24px 0.76px rgba(253, 160, 133, 0.5);
  color: #ffffff;
  line-height: 70px;
  text-align: center;
  font-size: 50px;
}
.colaboration-item .colaboration-content {
  width: calc(100% - 90px);
  padding-left: 27px;
}
.colaboration-item .colaboration-content .title {
  margin: 0;
  margin-bottom: 13px;
}
.colaboration-item .colaboration-content p {
  margin-bottom: 0 !important;
}
@media screen and (max-width: 399px) {
  .colaboration-item .colaboration-thumb {
    width: 60px;
    height: 60px;
    padding: 5px;
  }
  .colaboration-item .colaboration-thumb .icon {
    font-size: 36px;
    line-height: 50px;
  }
  .colaboration-item .colaboration-content {
    width: calc(100% - 60px);
    padding-left: 18px;
  }
  .colaboration-item .colaboration-content .title {
    font-size: 20px;
  }
}

.colaboration-slider {
  padding-bottom: 33px;
}

.cola-next, .cola-prev {
  font-size: 20px;
  line-height: 1;
  color: #ffffff;
  font-weight: 700;
}
.cola-next:hover, .cola-prev:hover {
  color: #ffffff;
}

.collaboration-anime-area {
  position: relative;
}
@media (max-width: 1199px) {
  .collaboration-anime-area .main-thumb img {
    width: 100%;
    max-width: 600px;
  }
}
.collaboration-anime-area .mobile,
.collaboration-anime-area .girl {
  position: absolute;
}
.collaboration-anime-area .mobile img,
.collaboration-anime-area .girl img {
  width: 100%;
}
.collaboration-anime-area .mobile {
  bottom: 90px;
  right: 295px;
}
@media screen and (max-width: 1300px) {
  .collaboration-anime-area .mobile {
    width: 210px;
    right: 320px;
  }
}
@media (max-width: 1199px) {
  .collaboration-anime-area .mobile {
    width: 180px;
    right: 180px;
  }
}
@media (max-width: 767px) {
  .collaboration-anime-area .mobile {
    width: 180px;
    right: 50%;
    transform: translate(50%);
    bottom: 50px;
  }
}
.collaboration-anime-area .mobile .show-up {
  opacity: 0;
}
.collaboration-anime-area .girl {
  width: 294px;
  bottom: 75px;
  right: 540px;
  z-index: 1;
}
@media screen and (max-width: 1300px) {
  .collaboration-anime-area .girl {
    width: 240px;
    right: 500px;
  }
}
@media (max-width: 1199px) {
  .collaboration-anime-area .girl {
    width: 200px;
    right: 340px;
  }
}
@media screen and (max-width: 530px) {
  .collaboration-anime-area .mobile {
    position: relative;
    width: 100%;
    right: 0;
    bottom: 0;
    transform: translateX(0);
    margin: 35px auto 0;
    max-width: 180px;
  }
  .collaboration-anime-area .girl {
    display: none;
  }
  .collaboration-anime-area .main-thumb {
    display: none;
  }
}

.mobile-slider {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 38px;
  padding: 4px;
}
@media screen and (max-width: 1300px) {
  .mobile-slider {
    border-radius: 26px;
  }
}
@media (max-width: 1199px) {
  .mobile-slider {
    border-radius: 22px;
  }
}
.mobile-slider::after {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: url(img/mobile-container.png) no-repeat center center;
  background-size: contain;
}
.mobile-slider .mobile-item,
.mobile-slider .owl-item,
.mobile-slider .owl-stage,
.mobile-slider .owl-stage-outer {
  width: 100%;
  height: 100%;
}

/*Smart Watch Section Starts Here*/
@media (min-width: 992px) {
  .smart-watch-content {
    max-width: 480px;
  }
}
@media (min-width: 768px) {
  .smart-watch-content .app-button-group {
    margin-top: -22px;
  }
}

.smart-watch-section {
  position: relative;
}

@media (max-width: 1399px) {
  .smart-watch img {
    max-width: 680px;
  }
}

.smart-bg {
  position: absolute;
  top: -160px;
  bottom: 0;
  left: 0;
  right: 0;
  background-position: right top;
  background-size: 100% 100%;
  z-index: -1;
}

/*Pricing Section Starts Here*/
@media (min-width: 992px) {
  .pricing-content {
    max-width: 480px;
  }
}
.pricing-content .button-group {
  margin-top: -10px;
}
@media (min-width: 768px) {
  .pricing-content .button-group {
    margin-top: -32px;
  }
}
@media (max-width: 991px) {
  .pricing-content {
    margin-bottom: 60px;
  }
}
@media (max-width: 767px) {
  .pricing-content {
    margin-bottom: 50px;
  }
}

.pricing-range {
  background: #ffffff;
  border-radius: 30px;
}
.pricing-range .pricing-range-top {
  background-image: linear-gradient(90deg, rgb(216, 216, 253) 0%, rgb(255, 255, 255) 100%);
  padding: 40px 0 0;
  border-radius: 30px 30px 0 0;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .pricing-range .pricing-range-top {
    padding-top: 30px;
  }
}
@media (max-width: 575px) {
  .pricing-range .pricing-range-top {
    padding-top: 30px;
  }
}
.pricing-range .pricing-header {
  margin-bottom: 116px;
}
.pricing-range .pricing-header .cate {
  display: block;
  text-align: center;
  text-transform: capitalize;
  margin-bottom: 17px;
  color: #ffffff;
}
.pricing-range .pricing-header .select-container {
  max-width: 270px;
  margin: 0 auto;
}
.pricing-range .pricing-header .select-bar {
  height: 44px;
  border: 1px solid rgb(202, 199, 246);
  background-color: rgba(105, 105, 201, 0.102);
  width: 100%;
  color: #ffffff;
  border-radius: 22px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .pricing-range .pricing-header {
    margin-bottom: 100px;
  }
}
@media (max-width: 575px) {
  .pricing-range .pricing-header {
    margin-bottom: 90px;
  }
}

.amount-area {
  width: 100%;
  margin-bottom: 30px;
}
.amount-area .item {
  text-align: center;
  margin-bottom: 30px;
  width: 50%;
}
.amount-area .item .title {
  margin: 0;
  line-height: 1;
  background: linear-gradient(-90deg, #4410e0 0%, #ff0089 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  font-weight: 600;
}
.amount-area .item .title sup {
  font-size: 50%;
  background: #ff0089;
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
.amount-area .item .info {
  display: block;
}
.amount-area .item:nth-child(odd) {
  border-right: 1px solid #8585c6;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .amount-area {
    margin-bottom: 0;
  }
  .amount-area .item .title {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .amount-area .item .title {
    font-size: 30px;
  }
  .amount-area .item .info {
    font-size: 16px;
  }
}
@media (max-width: 575px) {
  .amount-area {
    margin-bottom: 0;
  }
}

.invest-range-slider {
  height: 14px;
  border-radius: 7px;
  margin: 0 30px;
  transform: translateY(7px);
  background-color: rgb(255, 233, 207);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(134, 134, 231, 0.5);
  border: none;
}
.invest-range-slider .ui-slider-range {
  border-radius: 7px 0 0 7px;
  background-image: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
}
.invest-range-slider .ui-slider-handle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #dc3893;
  border: none;
  top: -10px;
  cursor: pointer;
  outline: none;
  transform: rotate(-45deg);
}
.invest-range-slider .ui-slider-handle::after, .invest-range-slider .ui-slider-handle::before {
  width: 8px;
  height: 8px;
}
.invest-range-slider .ui-slider-handle::after {
  top: 45%;
  left: 45%;
  border-left: 1px solid #ffffff;
  border-top: 1px solid #ffffff;
  transform: translate(-50%, -50%);
}
.invest-range-slider .ui-slider-handle::before {
  bottom: 45%;
  right: 45%;
  border-bottom: 1px solid #ffffff;
  transform: translate(50%, 50%);
  border-right: 1px solid #ffffff;
}

.pricing-range-bottom {
  padding: 55px 30px 40px;
  text-align: center;
  color: #504c89;
}
@media (min-width: 1200px) {
  .pricing-range-bottom .custom-button {
    padding: 15px 50px;
    border-radius: 30px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .pricing-range-bottom .custom-button {
    padding: 15px 50px;
    border-radius: 30px;
  }
}
.pricing-range-bottom .download-options {
  margin-top: 21px;
}
.pricing-range-bottom .download-options .custom-button {
  box-shadow: 0 -10px 10px rgba(238, 71, 48, 0.2);
}
@media (max-width: 1199px) and (min-width: 992px) {
  .pricing-range-bottom {
    padding: 50px 30px 40px;
  }
}
@media (max-width: 767px) {
  .pricing-range-bottom .custom-button {
    font-size: 14px;
  }
}
@media (max-width: 575px) {
  .pricing-range-bottom {
    padding: 45px 30px 30px;
  }
}
.pricing-range-bottom .title {
  color: #3b368c;
}

.download-options {
  margin: -7px;
  justify-content: center;
}
.download-options li {
  padding: 7px;
}
.download-options li a {
  width: 48px;
  height: 48px;
  line-height: 48px;
  background: #b7b9cf;
  color: #ffffff;
  border-radius: 50%;
  font-size: 24px;
}
.download-options li a:hover, .download-options li a.active {
  background: #ee4730;
}
@media (max-width: 767px) {
  .download-options {
    margin: -5px;
  }
  .download-options li {
    padding: 5px;
  }
  .download-options li a {
    width: 40px;
    height: 40px;
    font-size: 20px;
    line-height: 40px;
  }
}

.range-wrapper {
  position: relative;
}
@media (min-width: 992px) {
  .range-wrapper {
    padding-bottom: 100px;
    padding-top: 100px;
  }
}
@media (min-width: 992px) and (min-width: 1200px) {
  .range-wrapper {
    padding-bottom: 120px;
    padding-top: 120px;
  }
}
.range-wrapper .pricing-range {
  position: relative;
  z-index: 1;
}
.range-wrapper .shape-1, .range-wrapper .shape-2 {
  position: absolute;
}
.range-wrapper .shape-1 {
  top: 0;
  right: -190px;
}
.range-wrapper .shape-2 {
  bottom: 0;
  left: -90px;
}

.range-wrapper-2 .pricing-range .pricing-range-top {
  padding-left: 20px;
  padding-right: 20px;
}
.range-wrapper-2 .pricing-range .pricing-range-top .tags-area {
  text-align: center;
}
@media (max-width: 991px) {
  .range-wrapper-2 .pricing-range .pricing-range-top .tags-area {
    transform: translateY(-10px);
  }
}
.range-wrapper-2 .pricing-range .pricing-range-top .tags-area .tags {
  display: inline-block;
  background: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
  box-shadow: 4.232px 12.292px 10.56px 0.44px rgba(121, 107, 232, 0.5);
  color: #ffffff;
  padding: 17px 60px;
  border-radius: 40px;
  margin-top: -40px;
  margin-bottom: 30px;
}
@media (max-width: 991px) {
  .range-wrapper-2 .pricing-range .pricing-range-top .tags-area .tags {
    font-size: 28px;
    padding: 7px 40px;
    margin-bottom: 20px;
  }
}
@media (max-width: 767px) {
  .range-wrapper-2 .pricing-range .pricing-range-top .tags-area .tags {
    font-size: 20px;
    padding: 7px 40px;
  }
}
@media (max-width: 575px) {
  .range-wrapper-2 .pricing-range .pricing-range-top .tags-area .tags {
    padding: 5px 30px;
    font-size: 18px;
    margin-bottom: 10px;
  }
}
.range-wrapper-2 .pricing-range .pricing-range-top .select-bar {
  background: #6969c9;
}
.range-wrapper-2 .pricing-range .pricing-range-top .select-bar::after {
  border-color: #ffffff;
  right: 20px;
}
.range-wrapper-2 .pricing-range .pricing-range-top .select-bar .current {
  color: #ffffff;
}
.range-wrapper-2 .pricing-range .pricing-range-top .invest-range-slider {
  margin: 0;
}
.range-wrapper-2 .pricing-range .pricing-range-top .amount-area {
  margin-bottom: 5px;
}
.range-wrapper-2 .pricing-range .pricing-range-top .amount-area .info {
  color: #3b368c;
}
@media (min-width: 576px) {
  .range-wrapper-2 .pricing-range .pricing-range-top .pricing-header {
    margin-bottom: 100px;
  }
}
.range-wrapper-2 .pricing-range .pricing-range-top .pricing-header .cate {
  color: #3b368c;
}
.range-wrapper-2 .pricing-range .pricing-range-top .pricing-header .nice-select {
  color: #3b368c;
}
.range-wrapper-2 .pricing-range .pricing-range-bottom {
  justify-content: space-between;
  align-items: center;
}
.range-wrapper-2 .pricing-range .pricing-range-bottom .cate {
  display: block;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .range-wrapper-2 .pricing-range .pricing-range-bottom .cate {
    margin-bottom: 25px;
  }
}
@media (max-width: 575px) {
  .range-wrapper-2 .pricing-range .pricing-range-bottom .left, .range-wrapper-2 .pricing-range .pricing-range-bottom .right {
    width: 100%;
  }
  .range-wrapper-2 .pricing-range .pricing-range-bottom .left {
    margin-bottom: 22px;
  }
  .range-wrapper-2 .pricing-range .pricing-range-bottom .left .cate {
    margin-bottom: 20px;
  }
  .range-wrapper-2 .pricing-range .pricing-range-bottom .download-options {
    margin-top: 15px;
  }
}
@media (min-width: 768px) {
  .range-wrapper-2 .pricing-range .pricing-range-bottom {
    padding: 75px 30px 40px;
  }
}
@media (min-width: 768px) {
  .range-wrapper-2 .pricing-range .pricing-range-bottom,
  .range-wrapper-2 .pricing-range .pricing-range-top {
    padding-left: 50px;
    padding-right: 50px;
  }
}
@media (min-width: 992px) {
  .range-wrapper-2 .pricing-range .pricing-range-bottom,
  .range-wrapper-2 .pricing-range .pricing-range-top {
    padding-left: 90px;
    padding-right: 90px;
  }
}

.top-shape, .bottom-shape {
  position: absolute;
  z-index: 1;
  width: 102%;
  left: 50%;
  transform: translateX(-50%);
}
.top-shape img, .bottom-shape img {
  width: 100%;
}
@media (max-width: 1199px) {
  .top-shape, .bottom-shape {
    min-width: 1400px;
  }
  .top-shape.mw-0, .bottom-shape.mw-0 {
    min-width: 0;
  }
}

.top-shape {
  top: -3px;
}

.bottom-shape {
  bottom: -2px;
}

.ball-2,
.ball-3,
.ball-4,
.ball-5,
.ball-6,
.ball-7,
.ball-8 {
  position: absolute;
}
.ball-2 img,
.ball-3 img,
.ball-4 img,
.ball-5 img,
.ball-6 img,
.ball-7 img,
.ball-8 img {
  max-width: 100%;
}
@media (max-width: 767px) {
  .ball-2,
  .ball-3,
  .ball-4,
  .ball-5,
  .ball-6,
  .ball-7,
  .ball-8 {
    display: none;
  }
}

.ball-2 {
  opacity: 0.6;
  top: 40px;
  right: 15%;
}
@media (max-width: 991px) {
  .ball-2 {
    opacity: 0.3;
  }
}

.ball-3 {
  opacity: 0.6;
  top: 50%;
  right: 15%;
}
.ball-3.style2 {
  top: 5%;
  right: 30%;
  opacity: 1;
}

.ball-4 {
  opacity: 0.6;
  top: 110px;
  left: 5%;
}
@media (max-width: 991px) {
  .ball-4 {
    opacity: 0.3;
  }
}

.ball-5 {
  opacity: 0.6;
  top: 90px;
  left: 25%;
}
@media (max-width: 991px) {
  .ball-5 {
    opacity: 0.3;
  }
}

.ball-6 {
  opacity: 0.3;
  top: 45%;
  left: 5%;
}
.ball-6.style2 {
  top: 65%;
  left: 0%;
  opacity: 1;
}

.ball-7 {
  opacity: 0.3;
  top: 33%;
  left: 15%;
}

.ball-8 {
  opacity: 0.5;
  top: 33%;
  left: 15%;
}

/*Subscribe Seciton Starts Here*/
.subscribe-form {
  position: relative;
}
.subscribe-form input {
  height: 60px;
  border: 1px solid #c1c3f0;
  border-radius: 30px;
  color: #ffffff;
  padding-left: 30px;
  background: transparent;
}
.subscribe-form input::-moz-placeholder {
  color: #8e8ddc;
}
.subscribe-form input::placeholder {
  color: #8e8ddc;
}
.subscribe-form button {
  font-weight: 600;
  text-transform: uppercase;
  height: 60px;
  line-height: 60px;
  padding: 0 35px;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 17px 24px 0px rgba(18, 83, 252, 0.51);
  border: none;
  outline: none;
  border-radius: 30px;
  position: absolute;
  right: 0;
  top: 0;
  color: #ffffff;
  width: auto;
}
@media (max-width: 575px) {
  .subscribe-form input {
    height: 50px;
    padding-left: 20px;
    font-size: 14px;
  }
  .subscribe-form button {
    line-height: 50px;
    font-size: 14px;
    padding: 0 20px;
    height: 50px;
  }
}

@media (min-width: 992px) {
  .subscribe-thumb {
    margin-top: -80px;
  }
  .subscribe-pt {
    padding-top: 80px;
  }
}
@media (min-width: 1200px) {
  .subscribe-thumb {
    margin-top: -185px;
  }
  .subscribe-pt {
    padding-top: 185px;
  }
}
/*Coverage Section Starts Here*/
.coverage-right-area .rating-area {
  margin-bottom: 13px;
}
@media (min-width: 992px) {
  .coverage-right-area .rating-area {
    justify-content: flex-end;
  }
}
.coverage-right-area .rating-area .average {
  font-weight: 600;
  margin-left: 10px;
}
.coverage-right-area .amount {
  margin: 0;
  color: #38a20e;
  text-shadow: 1.452px 5.822px 8.64px rgba(56, 162, 14, 0.4);
  margin-bottom: 15px;
}
.coverage-right-area a {
  color: #ffffff;
}
.coverage-right-area a i {
  color: #337adb;
  margin-left: 10px;
}
@media (max-width: 767px) {
  .coverage-right-area .rating-area {
    margin-bottom: 5px;
  }
  .coverage-right-area .amount {
    margin-bottom: 5px;
  }
}

.coverage-header {
  margin-bottom: 35px;
}
@media (min-width: 768px) {
  .coverage-header {
    margin-bottom: 45px;
  }
}
@media (min-width: 992px) {
  .coverage-header {
    margin-bottom: 0;
  }
}

.coverage-wrapper {
  position: relative;
  margin-top: 45px;
}
@media (min-width: 768px) {
  .coverage-wrapper {
    margin-top: 60px;
  }
}
.coverage-wrapper div[class*=border-item] {
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.coverage-wrapper div[class*=border-item] * {
  margin: 0;
}
.coverage-wrapper div[class*=border-item] .title {
  font-weight: 600;
  line-height: 28px;
  margin-top: 10px;
}
.coverage-wrapper .border-item-1 {
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(232, 58, 153, 0.5);
}
.coverage-wrapper .border-item-2 {
  background: linear-gradient(0deg, rgb(131, 195, 250) 0%, rgb(102, 183, 255) 47%, rgb(98, 144, 251) 75%, rgb(94, 105, 246) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(94, 105, 246, 0.5);
}
.coverage-wrapper .border-item-3 {
  background: linear-gradient(0deg, rgb(246, 211, 101) 0%, rgb(253, 160, 133) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(253, 160, 133, 0.5);
}
.coverage-wrapper .border-item-4 {
  background: linear-gradient(0deg, rgb(209, 128, 221) 0%, rgb(121, 107, 232) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(121, 107, 232, 0.5);
}
.coverage-wrapper .border-item-5 {
  background: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
  box-shadow: 10.418px 30.257px 30.72px 1.28px rgba(121, 107, 232, 0.5);
}
.coverage-wrapper .border-item-6 {
  background: linear-gradient(0deg, rgb(151, 238, 133) 0%, rgb(100, 179, 244) 99%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(100, 179, 244, 0.5);
}
@media (min-width: 768px) {
  .coverage-wrapper {
    min-height: 609px;
  }
  .coverage-wrapper div[class*=border-item] {
    position: absolute;
    border-radius: 50%;
  }
  .coverage-wrapper div[class*=border-item] * {
    color: #ffffff;
    text-transform: capitalize;
  }
  .coverage-wrapper .border-item-1 {
    width: 214px;
    height: 214px;
    left: 70px;
    top: 75px;
  }
  .coverage-wrapper .border-item-1 .title {
    line-height: 1 !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .coverage-wrapper .border-item-1 {
    top: 0;
    left: 0;
  }
}
@media (min-width: 768px) {
  .coverage-wrapper .border-item-2 {
    width: 155px;
    height: 155px;
    background: linear-gradient(0deg, rgb(131, 195, 250) 0%, rgb(102, 183, 255) 47%, rgb(98, 144, 251) 75%, rgb(94, 105, 246) 100%);
    box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(94, 105, 246, 0.5);
    top: 80px;
    right: 155px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .coverage-wrapper .border-item-2 {
    top: 80px;
    right: 40px;
  }
}
@media (min-width: 768px) {
  .coverage-wrapper .border-item-2 .name {
    font-size: 16px;
  }
  .coverage-wrapper .border-item-2 .title {
    font-size: 40px;
  }
  .coverage-wrapper .border-item-3 {
    width: 155px;
    height: 155px;
    background: linear-gradient(0deg, rgb(246, 211, 101) 0%, rgb(253, 160, 133) 100%);
    box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(253, 160, 133, 0.5);
    top: 110px;
    right: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .coverage-wrapper .border-item-3 {
    top: 140px;
    right: 240px;
  }
}
@media (min-width: 768px) {
  .coverage-wrapper .border-item-3 .name {
    font-size: 16px;
  }
  .coverage-wrapper .border-item-3 .title {
    font-size: 40px;
  }
  .coverage-wrapper .border-item-4 {
    width: 155px;
    height: 155px;
    background: linear-gradient(0deg, rgb(209, 128, 221) 0%, rgb(121, 107, 232) 100%);
    box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(121, 107, 232, 0.5);
    bottom: 50px;
    left: 235px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .coverage-wrapper .border-item-4 {
    bottom: 0;
    left: 100px;
  }
}
@media (min-width: 768px) {
  .coverage-wrapper .border-item-4 .name {
    font-size: 16px;
  }
  .coverage-wrapper .border-item-4 .title {
    font-size: 40px;
  }
  .coverage-wrapper .border-item-5 {
    width: 100px;
    height: 100px;
    background: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
    box-shadow: 10.418px 30.257px 30.72px 1.28px rgba(121, 107, 232, 0.5);
    bottom: 175px;
    left: 500px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .coverage-wrapper .border-item-5 {
    left: 365px;
    bottom: 170px;
  }
}
@media (min-width: 768px) {
  .coverage-wrapper .border-item-5 .name {
    font-size: 16px;
  }
  .coverage-wrapper .border-item-5 .title {
    font-size: 28px;
    margin-top: 0 !important;
  }
  .coverage-wrapper .border-item-6 {
    width: 100px;
    height: 100px;
    background: linear-gradient(0deg, rgb(151, 238, 133) 0%, rgb(100, 179, 244) 99%);
    box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(100, 179, 244, 0.5);
    right: 130px;
    bottom: 110px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .coverage-wrapper .border-item-6 {
    right: 40px;
    bottom: 60px;
  }
}
@media (min-width: 768px) {
  .coverage-wrapper .border-item-6 .name {
    font-size: 16px;
  }
  .coverage-wrapper .border-item-6 .title {
    font-size: 28px;
    margin-top: 0 !important;
  }
}
@media (max-width: 767px) {
  .coverage-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: -20px;
  }
  .coverage-wrapper div[class*=border-item] {
    width: calc(33.3333333333% - 13.33px);
    margin-bottom: 20px;
    padding: 20px 10px;
    border-radius: 5px;
  }
  .coverage-wrapper div[class*=border-item] * {
    color: #ffffff;
    margin: 0;
  }
  .coverage-wrapper div[class*=border-item] .name {
    font-size: 16px;
  }
}
@media screen and (max-width: 767px) and (max-width: 499px) {
  .coverage-wrapper div[class*=border-item] {
    width: calc(50% - 10px);
  }
}

/*Feature Section Starts Here*/
.feature-item {
  padding: 30px;
  border: 1px solid #acacf4;
  border-radius: 10px;
  margin-bottom: 30px;
  position: relative;
  transition: all ease 0.3s;
  cursor: pointer;
}
.feature-item .feature-thumb {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 1px solid #cecbf5;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: rotate 3s linear infinite;
  -webkit-animation: rotate 3s linear infinite;
  -moz-animation: rotate 3s linear infinite;
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
  -moz-animation-play-state: paused;
}
.feature-item .feature-thumb::after, .feature-item .feature-thumb::before {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background-image: linear-gradient(0deg, rgb(246, 211, 101) 0%, rgb(253, 160, 133) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(253, 160, 133, 0.5);
}
.feature-item .feature-thumb::after {
  top: 6px;
  right: 6px;
}
.feature-item .feature-thumb::before {
  bottom: 6px;
  left: 6px;
}
.feature-item .feature-thumb .thumb {
  width: 71px;
  height: 71px;
  border-radius: 50%;
  animation: rev-rotate 3s linear infinite;
  -webkit-animation: rev-rotate 3s linear infinite;
  -moz-animation: rev-rotate 3s linear infinite;
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
  -moz-animation-play-state: paused;
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
}
.feature-item .feature-thumb .thumb img {
  width: 100%;
}
.feature-item:nth-of-type(4n + 2) .feature-thumb::after, .feature-item:nth-of-type(4n + 2) .feature-thumb::before {
  background-image: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
  box-shadow: 10.418px 30.257px 30.72px 1.28px rgba(121, 107, 232, 0.5);
}
.feature-item:nth-of-type(4n + 2) .feature-thumb .thumb {
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(121, 107, 232, 0.5);
}
.feature-item:nth-of-type(4n + 3) .feature-thumb::after, .feature-item:nth-of-type(4n + 3) .feature-thumb::before {
  background-image: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
}
.feature-item:nth-of-type(4n + 3) .feature-thumb .thumb {
  box-shadow: 2.419px 9.703px 18.24px 0.76px rgba(253, 160, 133, 0.5);
}
.feature-item:nth-of-type(4n + 4) .feature-thumb::after, .feature-item:nth-of-type(4n + 4) .feature-thumb::before {
  background-image: linear-gradient(0deg, rgb(209, 128, 221) 0%, rgb(121, 107, 232) 100%);
}
.feature-item:nth-of-type(4n + 4) .feature-thumb .thumb {
  box-shadow: 4.232px 12.292px 10.56px 0.44px rgba(121, 107, 232, 0.5);
}
.feature-item .feature-content {
  width: calc(100% - 90px);
  padding-left: 30px;
}
.feature-item .feature-content .title {
  margin: 0;
  margin-bottom: 29px;
}
@media (max-width: 767px) {
  .feature-item .feature-content .title {
    margin-bottom: 20px;
  }
}
.feature-item::before {
  width: 100%;
  height: 100%;
  background: url(img/feature-bg.png) no-repeat center center;
  background-size: cover;
  transition: all ease 0.3s;
  opacity: 0;
  border-radius: 10px;
}
.feature-item * {
  position: relative;
  z-index: 1;
}
.feature-item.active, .feature-item:hover {
  border-color: transparent;
}
.feature-item.active .feature-thumb, .feature-item.active .feature-thumb .thumb, .feature-item:hover .feature-thumb, .feature-item:hover .feature-thumb .thumb {
  animation-play-state: running;
  -webkit-animation-play-state: running;
  -moz-animation-play-state: running;
}
.feature-item.active .feature-content .title, .feature-item:hover .feature-content .title {
  color: #ffffff;
}
.feature-item.active .feature-content p, .feature-item:hover .feature-content p {
  color: rgba(255, 255, 255, 0.9);
}
.feature-item.active::before, .feature-item:hover::before {
  opacity: 1;
}
@media (max-width: 575px) {
  .feature-item {
    padding: 30px 20px;
  }
}
@media screen and (max-width: 499px) {
  .feature-item .feature-thumb {
    margin-bottom: 20px;
  }
  .feature-item .feature-content {
    padding: 0;
    width: 100%;
  }
}
.feature-item::after {
  width: 100%;
  height: 100%;
  z-index: 99;
}

.feature--thumb {
  width: 822px;
}
@media (max-width: 1199px) {
  .feature--thumb {
    width: 700px;
  }
}
@media (max-width: 991px) {
  .feature--thumb {
    margin: 0 auto 40px;
    width: 100%;
    max-width: 450px;
  }
  .feature--thumb img {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .feature--thumb {
    max-width: 400px;
  }
}
@media (min-width: 992px) {
  .feature--thumb.style-two {
    width: 650px;
  }
  .feature--thumb.style-two .owl-item .main-thumb img {
    width: 100%;
  }
}
@media (min-width: 992px) and (min-width: 1200px) {
  .feature--thumb.style-two {
    width: 1000px;
  }
}
@media (min-width: 992px) and (min-width: 1200px) and (min-width: 1400px) {
  .feature--thumb.style-two {
    width: 1100px;
  }
  .feature--thumb.style-two img {
    width: auto !important;
  }
}
@media (min-width: 992px) {
  .feature--thumb.style-two .owl-item .main-thumb {
    direction: rtl;
    width: auto;
  }
}

.feature-shapes {
  position: absolute;
  right: 0;
  top: 0;
}
.feature-shapes img {
  width: 100%;
  max-width: 595px;
}
@media (max-width: 1399px) {
  .feature-shapes {
    max-width: 395px;
  }
}
.feature-shapes.style-two {
  left: 0;
  right: auto;
  max-width: 695px;
}

/*How Section Starts Here*/
.how-item {
  margin-bottom: 40px;
  text-align: center;
}
.how-item .how-thumb {
  position: relative;
  margin-bottom: 40px;
}
.how-item .how-thumb::before {
  position: absolute;
  counter-increment: how-counter;
  content: counter(how-counter);
  border-radius: 46px 43px 68px 38px;
  height: 70px;
  width: 68px;
  line-height: 70px;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(232, 58, 153, 0.5);
  color: #ffffff;
  font-family: "Josefin Sans", sans-serif;
  font-size: 54px;
  top: 0;
  left: 0;
}
@media (max-width: 991px) {
  .how-item .how-thumb::before {
    font-size: 40px;
  }
}
@media (max-width: 991px) and (max-width: 575px) {
  .how-item .how-thumb::before {
    font-size: 30px;
    width: 50px;
    height: 50px;
    line-height: 50px;
  }
}
.how-item .how-thumb img {
  max-width: 100%;
}
.how-item .how-content .download-options {
  margin-bottom: 28px;
}
.how-item .how-content .download-options li a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-size: 20px;
}
.how-item .how-content .download-options li a.active, .how-item .how-content .download-options li a:hover {
  background: linear-gradient(-127deg, rgb(93, 39, 198) 29%, rgb(46, 52, 255) 100%);
  box-shadow: 0.975px 7.94px 12px 0px rgba(47, 105, 252, 0.47);
}
.how-item .how-content .button-3 {
  margin-bottom: 35px;
  font-size: 16px;
  padding: 15px 0;
  display: block;
}
.how-item .how-content .title {
  margin-bottom: 32px;
}
.how-item .how-content .title a {
  text-decoration: underline;
  color: #3036ff;
}
@media (max-width: 991px) {
  .how-item {
    max-width: 280px;
    margin-left: auto;
    margin-right: auto;
  }
  .how-item .how-thumb {
    margin-bottom: 30px;
  }
  .how-item .button-3 {
    margin-bottom: 25px;
  }
  .how-item .title {
    margin-bottom: 22px;
  }
  .how-item .download-options {
    margin-bottom: 18px;
  }
}

.row {
  counter-reset: how-counter;
}
.row div[class*=col]:nth-of-type(3n + 2) .how-item .how-thumb::before {
  background: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
  box-shadow: 4.232px 12.292px 15.36px 0.64px rgba(209, 62, 157, 0.5);
}
.row div[class*=col]:nth-of-type(3n + 3) .how-item .how-thumb::before {
  background: linear-gradient(0deg, rgb(246, 211, 101) 0%, rgb(253, 160, 133) 100%);
  box-shadow: 2.661px 10.673px 13.44px 0.56px rgba(151, 134, 20, 0.5);
}

/*Amazing Feature Section Starts Here*/
.feature-video-area {
  position: relative;
  z-index: 9;
  border-radius: 50%;
  overflow: hidden;
}
@media (max-width: 1199px) {
  .feature-video-area .thumb img {
    max-width: 600px;
  }
}
.feature-video-area .button-area {
  text-align: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.feature-video-area .button-area .title {
  color: #ffffff;
  margin-bottom: 40px;
  width: 100%;
}
@media (max-width: 575px) {
  .feature-video-area .button-area .title {
    margin-bottom: 20px;
  }
}
@media (max-width: 575px) {
  .feature-video-area .button-area {
    height: 100%;
    width: 100%;
  }
}
@media (max-width: 991px) {
  .feature-video-area {
    max-width: 450px;
  }
  .feature-video-area .thumb img {
    width: 100%;
  }
}

.feature-top-shape {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  min-width: 1920px;
  top: -3px;
}
@media screen and (max-width: 1550px) {
  .feature-top-shape {
    left: auto;
    transform: translateX(0);
    right: -175px;
  }
}
.feature-top-shape img {
  width: 100%;
}

.am-item {
  padding: 80px 20px;
  text-align: center;
  margin-bottom: 30px;
  border-radius: 20px;
  box-shadow: 1.95px 15.881px 30px 0px rgba(47, 105, 252, 0.1);
  background-color: #31377d;
  transition: all ease 0.3s;
}
.am-item .am-thumb {
  height: 110px;
  margin-bottom: 45px;
}
.am-item .am-thumb img {
  height: 100%;
}
.am-item .am-content .title {
  font-weight: 700;
  color: #ffffff;
}
@media (min-width: 992px) and (max-width: 1399px) {
  .am-item {
    padding: 50px 20px;
  }
  .am-item .am-thumb {
    height: 80px;
  }
}
@media (max-width: 575px) {
  .am-item {
    max-width: 280px;
    margin: 0 auto 30px;
  }
}
.am-item:hover, .am-item.active {
  box-shadow: 1.95px 15.881px 30px 0px rgba(47, 105, 252, 0.31);
}

.feature-background {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
@media (min-width: 992px) {
  .feature-background {
    bottom: 80px;
  }
}

@media (min-width: 992px) {
  .amazing-feature-bottom {
    margin-top: -95px;
  }
  .amazing-feature-bottom .section-header p {
    max-width: 520px;
  }
}

.shape-container {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.downarrow {
  position: absolute;
  left: 100%;
  top: 20px;
}

.ft-shape {
  position: absolute;
  top: 0;
  left: 0;
}
@media (max-width: 1399px) {
  .ft-shape {
    display: none;
  }
}
.ft-shape img {
  max-width: 100%;
}

.rocket {
  position: absolute;
  top: 0;
  left: -10%;
}

.am-obj {
  position: absolute;
  top: 30%;
  left: 1540px;
}
.am-obj.two {
  top: 70%;
  left: 1600px;
  animation: zigZag 60s alternate infinite;
}

/*Sponsor Section Starts Here*/
@media (max-width: 991px) {
  .spon-cont {
    margin-bottom: 40px;
    text-align: center;
  }
}

.sponsor-thumb {
  max-width: 95px;
  margin: 0 auto;
}
.sponsor-thumb img {
  max-width: 100%;
}

/*Download Section Starts Here*/
.download-area {
  max-width: 735px;
  margin: 0 auto 15px;
  justify-content: space-between;
}
@media (min-width: 768px) {
  .download-area {
    margin-bottom: 35px;
  }
}

.download-item {
  margin-bottom: 25px;
  padding: 0 15px;
  text-align: center;
}
.download-item .thumb {
  width: 94px;
  height: 94px;
  text-align: center;
  line-height: 94px;
  font-size: 60px;
  color: #ffffff;
  background: #31377d;
  border-radius: 50%;
  margin: 0 auto 14px;
}
.download-item .content .title {
  font-family: "Open Sans", sans-serif;
  font-size: 18px;
  margin: 0;
  font-weight: 600;
  text-transform: capitalize;
}
.download-item:hover .thumb, .download-item.active .thumb {
  background: #ee4730;
}
@media (max-width: 767px) {
  .download-item .thumb {
    font-size: 40px;
    width: 70px;
    height: 70px;
    line-height: 70px;
  }
}
@media (max-width: 575px) {
  .download-item {
    padding: 0 5px;
    width: 50%;
  }
  .download-item .thumb {
    width: 70px;
    height: 70px;
    line-height: 70px;
    font-size: 30px;
  }
}

/*Custom Plan Section Starts Here*/
.custom-plan {
  background-size: 100% 100%;
}
@media (max-width: 1199px) {
  .custom-plan {
    background-image: none !important;
  }
}

.custom-wrapper {
  position: relative;
  padding: 220px 0 180px;
}
.custom-wrapper .section-header {
  max-width: 700px;
}
@media (min-width: 768px) {
  .custom-wrapper .section-header {
    margin-bottom: 45px;
  }
}
.custom-wrapper .circle {
  display: block;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 15px;
  overflow: hidden;
  background: linear-gradient(-65deg, #2621b5 0%, #891dbd 100%);
  animation: rotate 1s linear infinite;
}
@media (min-width: 768px) {
  .custom-wrapper .circle {
    display: none;
  }
}
.custom-wrapper .circle::after {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ffffff;
}
.custom-wrapper .circle.two {
  left: 30%;
  top: calc(100% - 40px);
}
.custom-wrapper .custom-area {
  position: relative;
  z-index: 1;
}
.custom-wrapper .calculate-bg {
  height: 100%;
  height: 100%;
}
.custom-wrapper .calculate-bg img {
  height: 100%;
}
.custom-wrapper .custom-button {
  text-transform: capitalize;
}
@media (max-width: 1199px) {
  .custom-wrapper {
    padding: 180px 0;
  }
}

.theme-shadow {
  box-shadow: 0px 5px 20px rgba(238, 71, 48, 0.5);
}

/*Counter Section Statrs Here*/
.counter-area {
  justify-content: space-between;
  margin: 0 -15px -27px;
}

.counter--item {
  margin: 0 15px 20px;
}
.counter--item .counter-thumb {
  width: 80px;
}
.counter--item .counter-thumb img {
  width: 100%;
}
.counter--item .counter-content {
  width: calc(100% - 80px);
  padding-left: 30px;
}
.counter--item .counter-content .title {
  margin: 0;
}
.counter--item .counter-content .title span {
  color: #ff8a00;
}
@media (min-width: 768px) {
  .counter--item .counter-content .title {
    font-size: 60px;
    margin-bottom: 2px;
  }
}
@media (min-width: 768px) and (min-width: 992px) and (max-width: 1199px) {
  .counter--item .counter-content .title {
    font-size: 48px;
  }
}
.counter--item .counter-content span {
  color: rgba(255, 255, 255, 0.9);
}
@media (max-width: 575px) {
  .counter--item .counter-thumb {
    width: 60px;
  }
  .counter--item .counter-content {
    width: calc(100% - 60px);
    padding-left: 20px;
  }
}

.counter-thumb-1 {
  position: relative;
  z-index: 1;
}
@media (max-width: 1199px) {
  .counter-thumb-1 img {
    max-width: 700px;
  }
}

@media (max-width: 991px) {
  .counter-section {
    background-color: #2c18b4;
  }
}

div[class*=elem] {
  position: absolute;
}
@media (max-width: 991px) {
  div[class*=elem] {
    display: none;
  }
}
div[class*=elem] img {
  max-width: 100%;
}

.elem-1 {
  opacity: 0.5;
  top: 33%;
  animation: lefTRight 25s linear infinite;
  -webkit-animation: lefTRight 25s linear infinite;
  -moz-animation: lefTRight 25s linear infinite;
}

.elem-2 {
  top: 30%;
  animation: righTLeft 25s linear infinite;
  -webkit-animation: righTLeft 25s linear infinite;
  -moz-animation: righTLeft 25s linear infinite;
}

.elem-3 {
  top: 33%;
  left: 40%;
  animation: zigZag 25s alternate infinite;
  -webkit-animation: zigZag 25s alternate infinite;
  -moz-animation: zigZag 25s alternate infinite;
}

.elem-4 {
  top: 60%;
  left: 90%;
  animation: zigZag 25s alternate-reverse infinite;
  -webkit-animation: zigZag 25s alternate-reverse infinite;
  -moz-animation: zigZag 25s alternate-reverse infinite;
}

.elem-5 {
  bottom: 12%;
  left: 35%;
  animation: rotate 40s linear infinite;
  -webkit-animation: rotate 40s linear infinite;
  -moz-animation: rotate 40s linear infinite;
}

.elem-6 {
  top: 40%;
  left: 10%;
  animation: zigZag 50s alternate infinite;
  -webkit-animation: zigZag 50s alternate infinite;
  -moz-animation: zigZag 50s alternate infinite;
}

.elem-7 {
  bottom: 15%;
  left: 15%;
  animation: zigZag 25s alternate-reverse infinite;
  -webkit-animation: zigZag 25s alternate-reverse infinite;
  -moz-animation: zigZag 25s alternate-reverse infinite;
}

/*How Section Two Starts Here*/
.downarrow2 {
  text-align: right;
  transform: translateX(-150px);
}
@media (min-width: 992px) {
  .downarrow2 {
    transform: translateX(-150px);
  }
}
@media screen and (max-width: 525px) {
  .downarrow2 {
    transform: translateX(-70px);
  }
  .downarrow2 img {
    max-height: 70px;
  }
}
.downarrow2 img {
  max-width: 100%;
}

.how-bottom, .how-top {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  min-width: 1920px;
  width: 100%;
}
.how-bottom img, .how-top img {
  width: 100%;
}

.how-top {
  top: 0;
}

.how-bottom {
  bottom: 100px;
}

.how-main-bg {
  top: 0;
  left: 0;
  height: calc(100% - 135px);
  width: 100%;
}

.how--item {
  align-items: center;
  justify-content: space-between;
  margin-bottom: 100px;
  position: relative;
}
.how--item .how-content {
  position: relative;
  text-align: center;
  max-width: 255px;
}
.how--item .how-content .button-4 {
  display: block;
  padding: 16px 5px;
  font-size: 16px;
  text-align: center;
  margin-bottom: 34px;
}
.how--item .how-content .serial {
  top: 20%;
}
.how--item .how-content * {
  position: relative;
  z-index: 1;
}
.how--item .how-content .thumb {
  height: 140px;
  margin-bottom: 30px;
}
.how--item .how-content .thumb img {
  height: 100%;
}
.how--item .how-content p {
  color: #ffffff;
}
@media (min-width: 768px) {
  .how--item .how-thumb {
    max-width: calc(90% - 255px);
  }
}
.how--item:nth-of-type(2n + 2) {
  flex-direction: row-reverse;
}
.how--item:nth-of-type(2n + 2) .how-thumb {
  direction: rtl;
}
@media (min-width: 992px) {
  .how--item:nth-of-type(2n + 1) .how-content {
    margin-left: 8.333333%;
  }
  .how--item:nth-of-type(2n + 2) .how-content {
    margin-right: 8.333333%;
  }
}
.how--item .anime-item {
  position: absolute;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background: linear-gradient(-127deg, rgb(0, 21, 254) 0%, rgb(254, 54, 192) 88%);
  z-index: 1;
}
@media (max-width: 767px) {
  .how--item .anime-item {
    display: none;
  }
}
.how--item .anime-item::after, .how--item .anime-item::before {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(-127deg, rgb(0, 21, 254) 0%, rgb(254, 54, 192) 88%);
  box-shadow: 3.694px -4.728px 38px 0px rgba(17, 65, 189, 0.52);
}
.how--item .anime-item::before {
  animation: inner-ripple 2000ms linear infinite;
}
.how--item .anime-item::after {
  animation: outer-ripple 2000ms linear infinite;
}
@media (max-width: 767px) {
  .how--item {
    margin-bottom: 120px;
  }
  .how--item .how-content {
    margin: 0 auto 40px;
  }
  .how--item .how-thumb img {
    width: 100%;
  }
}

.how-wrapper {
  position: relative;
  padding-top: 45px;
}
.how-wrapper .plane {
  z-index: 3;
  position: absolute;
  left: calc(50% - 46px);
  bottom: calc(100% - 20px);
}
.how-wrapper::after {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: calc(100% + 150px);
  background: linear-gradient(-127deg, rgb(148, 65, 212) 15%, rgb(255, 81, 255) 100%);
}
@media (max-width: 767px) {
  .how-wrapper {
    padding-top: 80px;
  }
  .how-wrapper::after {
    display: none;
  }
}

.member-counter-area {
  position: relative;
  margin-top: 160px;
}
.member-counter-area .member-bg {
  width: 100%;
  height: 100%;
  text-align: center;
  min-width: 720px;
}
.member-counter-area .member-bg img {
  height: 100%;
  max-width: 100%;
}
.member-counter-area .up-bal {
  position: absolute;
  bottom: 99%;
  left: calc(50% - 268px);
  width: 555px;
}
.member-counter-area .up-bal img {
  max-width: 100%;
}
.member-counter-area .up-bal.two {
  bottom: 75%;
  left: calc(50% - 366px);
  width: 700px;
}
.member-counter-area .member-counter {
  position: relative;
  z-index: 1;
  text-align: center;
}
.member-counter-area .member-counter .title {
  margin-bottom: 23px;
}
.member-counter-area .member-counter p {
  margin-bottom: 49px;
}
.member-counter-area .member-counter .custom-button {
  border-radius: 30px;
  padding: 15px 45px;
  margin-bottom: 28px;
  box-shadow: 0px 10px 20px rgba(238, 71, 48, 0.9);
}
.member-counter-area .member-counter .download-options li a {
  background: #b7b9cf;
}
.member-counter-area .member-counter .download-options li a i {
  color: #553fdc;
}
.member-counter-area .member-counter .download-options li a.active, .member-counter-area .member-counter .download-options li a:hover {
  background: #ee4730;
}
.member-counter-area .member-counter .download-options li a.active i, .member-counter-area .member-counter .download-options li a:hover i {
  color: #ffffff;
}
@media (max-width: 767px) {
  .member-counter-area .member-counter .title {
    margin-bottom: 10px;
  }
  .member-counter-area .member-counter p {
    margin-bottom: 30px;
  }
  .member-counter-area .member-counter .custom-button {
    margin-bottom: 30px;
  }
}

/*Real Data Section Starts Here*/
.bal-list li {
  padding: 0;
  font-weight: 600;
  color: #ffffff;
  width: 100%;
  max-width: 285px;
  padding-left: 30px;
  position: relative;
  margin-bottom: 3px;
}
.bal-list li::before {
  position: absolute;
  content: "\f00c";
  left: 0;
  top: 0;
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
  color: #ffffff;
  font-size: 12px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .bal-list li {
    max-width: 255px;
  }
}
@media (min-width: 576px) and (max-width: 767px) {
  .bal-list li {
    max-width: 255px;
  }
}
@media (max-width: 575px) {
  .bal-list li {
    max-width: 100%;
  }
}

.realtime-content .bal-list {
  margin-bottom: 25px;
}
@media (min-width: 768px) {
  .realtime-content .bal-list {
    margin-bottom: 35px;
  }
}

.reliable-top, .reliable-bottom {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  min-width: 1920px;
}
.reliable-top img, .reliable-bottom img {
  width: 100%;
}

.reliable-top {
  top: -2px;
}

.reliable-bottom {
  bottom: -2px;
}

.reliable-video {
  position: relative;
}
.reliable-video img {
  width: 100%;
}

/*Convencing Feature Section Starts Here*/
.feature-tabs {
  border: none;
  justify-content: space-between;
  margin: 0 -10px 25px;
}
.feature-tabs li {
  margin-bottom: 30px;
  padding: 0 10px;
}
.feature-tabs li a, .feature-tabs li .nav-link {
  text-align: center;
  display: block;
  border: transparent;
  height: initial;
}
.feature-tabs li a .thumb, .feature-tabs li .nav-link .thumb {
  width: 70px;
  height: 70px;
  display: block;
  line-height: 70px;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  font-size: 36px;
  color: #ffffff;
  margin: 0 auto 20px;
}
.feature-tabs li a .subtitle, .feature-tabs li .nav-link .subtitle {
  text-transform: capitalize;
  color: #ffffff;
}
.feature-tabs li a.active, .feature-tabs li a:focus, .feature-tabs li a:focus-visible, .feature-tabs li .nav-link.active, .feature-tabs li .nav-link:focus, .feature-tabs li .nav-link:focus-visible {
  background-color: transparent;
  border-color: transparent;
}
.feature-tabs li a.active .subtitle, .feature-tabs li a:focus .subtitle, .feature-tabs li a:focus-visible .subtitle, .feature-tabs li .nav-link.active .subtitle, .feature-tabs li .nav-link:focus .subtitle, .feature-tabs li .nav-link:focus-visible .subtitle {
  color: #ee4730;
}
.feature-tabs li:nth-of-type(5n + 2) button .thumb {
  background: linear-gradient(0deg, rgb(246, 211, 101) 0%, rgb(253, 160, 133) 100%);
  box-shadow: 2.419px 9.703px 18.24px 0.76px rgba(253, 160, 133, 0.5);
}
.feature-tabs li:nth-of-type(5n + 3) button .thumb {
  background: linear-gradient(0deg, rgb(149, 180, 255) 0%, rgb(115, 151, 255) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(115, 151, 255, 0.5);
}
.feature-tabs li:nth-of-type(5n + 4) button .thumb {
  background: linear-gradient(0deg, rgb(157, 243, 167) 0%, rgb(87, 212, 100) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(87, 212, 100, 0.5);
}
.feature-tabs li:nth-of-type(5n + 5) button .thumb {
  background: linear-gradient(0deg, rgb(99, 238, 247) 0%, rgb(58, 209, 229) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(58, 209, 229, 0.5);
}
@media (max-width: 991px) {
  .feature-tabs li {
    margin-bottom: 20px;
  }
  .feature-tabs li button .thumb {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 24px;
    margin-bottom: 10px;
  }
  .feature-tabs li button .subtitle {
    font-size: 14px;
  }
}
@media (max-width: 575px) {
  .feature-tabs li {
    width: calc(50% - 20px);
  }
}

.convencing-feature-content .title {
  margin-bottom: 28px;
}
.convencing-feature-content p {
  margin-bottom: 38px;
}
@media (max-width: 767px) {
  .convencing-feature-content .title {
    margin-bottom: 20px;
  }
  .convencing-feature-content p {
    margin-bottom: 28px;
  }
}

@media (max-width: 991px) {
  .convencing-feature-thumb img {
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .convencing-feature-thumb {
    margin-bottom: 40px;
  }
}

/*Counter Section Two Start Here*/
.counter-wrapper-area-2 {
  padding: 75px 0 76px;
  border-radius: 30px;
  background: #31377d;
  box-shadow: 1.21px 4.851px 19.2px 0.8px rgba(60, 57, 213, 0.1);
  position: relative;
  z-index: 1;
  margin-top: 50px;
}
@media (min-width: 1200px) {
  .counter-wrapper-area-2 {
    transform: translateY(120px);
    margin-top: -90px;
  }
}
@media (max-width: 575px) {
  .counter-wrapper-area-2 {
    padding: 52px 0 53px;
  }
}

.counter-wrapper-2 {
  margin-bottom: -30px;
}

.counter-item-2 {
  width: 25%;
  text-align: center;
  margin-bottom: 30px;
  border-right: 1px solid #bccaea;
}
.counter-item-2 .title {
  align-items: center;
  justify-content: center;
  margin-bottom: -5px;
  margin-top: 0;
}
.counter-item-2:nth-of-type(4n + 4) {
  border: none;
}
@media (max-width: 991px) {
  .counter-item-2 .title {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .counter-item-2 {
    width: 50%;
  }
  .counter-item-2:nth-of-type(4n + 2) {
    border: none;
  }
}
@media (max-width: 575px) {
  .counter-item-2 .name {
    font-size: 14px;
  }
}

/*Advance Feature Section Starts Here*/
.advance-feature-item {
  margin: 0 -15px;
  align-items: center;
}
.advance-feature-item .advance-feature-thumb, .advance-feature-item .advance-feature-content {
  width: 50%;
  padding: 0 15px;
}
.advance-feature-item .advance-feature-thumb img, .advance-feature-item .advance-feature-content img {
  width: 100%;
}
@media (max-width: 991px) {
  .advance-feature-item .advance-feature-thumb, .advance-feature-item .advance-feature-content {
    width: 100%;
  }
}
@media (max-width: 991px) {
  .advance-feature-item .advance-feature-thumb {
    max-width: 450px;
    margin-bottom: 40px;
  }
}
@media (min-width: 992px) {
  .advance-feature-item:nth-child(even) {
    flex-direction: row-reverse;
  }
}
.advance-feature-item.style-two .advance-feature-thumb {
  width: calc(100% - 610px);
}
.advance-feature-item.style-two .advance-feature-content {
  width: 610px;
  padding: 0 15px;
}
.advance-feature-item.style-two .advance-feature-content img {
  width: 100%;
}
@media (max-width: 1199px) {
  .advance-feature-item.style-two .advance-feature-thumb, .advance-feature-item.style-two .advance-feature-content {
    width: 50%;
  }
}
@media (max-width: 991px) {
  .advance-feature-item.style-two .advance-feature-thumb, .advance-feature-item.style-two .advance-feature-content {
    width: 100%;
  }
}
@media (min-width: 1400px) {
  .advance-feature-item.style-two {
    justify-content: space-between;
  }
  .advance-feature-item.style-two .advance-feature-thumb {
    max-width: 540px;
  }
  .advance-feature-item.style-two .advance-feature-thumb img {
    width: auto;
  }
}
@media screen and (min-width: 1400px) and (min-width: 1650px) {
  .advance-feature-item.style-two .advance-feature-thumb {
    max-width: 410px;
  }
}
@media (min-width: 1400px) {
  .advance-feature-item.style-two:nth-child(odd) .advance-feature-thumb {
    direction: rtl;
  }
  .advance-feature-item.style-two:nth-child(odd) .advance-feature-thumb img {
    max-width: unset;
  }
}

/*To Access Section Stars Here*/
.to-access-item {
  border-radius: 20px;
  background-color: rgba(225, 232, 255, 0.702);
  box-shadow: 3.871px 15.525px 68px 0px rgba(29, 40, 143, 0.5);
  padding: 60px 15px;
  text-align: center;
  margin: 0 auto 30px;
}
@media (max-width: 575px) {
  .to-access-item {
    max-width: 350px;
  }
}
.to-access-item .to-access-thumb {
  margin: 0 auto 28px;
  width: 90px;
  height: 90px;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;
}
.to-access-item .to-access-thumb span {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid #c2c6f7;
  animation: rotate2 8s linear infinite;
  -webkit-animation: rotate2 8s linear infinite;
  -moz-animation: rotate2 8s linear infinite;
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
  -moz-animation-play-state: paused;
}
.to-access-item .to-access-thumb span::before, .to-access-item .to-access-thumb span::after {
  width: 10px;
  height: 10px;
  background-size: contain;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 1.21px 4.851px 6.72px 0.28px rgba(232, 58, 153, 0.39);
  left: 5px;
  bottom: 5px;
}
.to-access-item .to-access-thumb span::after {
  width: 13px;
  height: 13px;
  left: auto;
  bottom: auto;
  right: 5px;
  top: 5px;
}
.to-access-item .to-access-thumb .thumb {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(219, 218, 255, 0.5);
  background: #ffffff;
}
.to-access-item .to-access-thumb .thumb img {
  width: 100%;
}
.to-access-item.active .to-access-thumb span, .to-access-item:hover .to-access-thumb span {
  animation-play-state: running;
  -webkit-animation-play-state: running;
  -moz-animation-play-state: running;
}
.to-access-item.style-two {
  box-shadow: none;
  background: #31377d;
}

/*Work Section Starts Here*/
.work-item {
  max-width: 540px;
  margin: 0 auto 40px;
  text-align: center;
}
@media (min-width: 768px) {
  .work-item {
    padding-top: 10px;
  }
}
.work-item .work-thumb {
  width: 288px;
  margin: 0 auto 30px;
}
.work-item .work-thumb img {
  max-width: 100%;
}
.work-item .work-content .title {
  margin-bottom: 28px;
}
.work-item .work-content p {
  margin-bottom: 38px;
}
@media (max-width: 575px) {
  .work-item .work-thumb {
    max-width: 180px;
    margin-bottom: 10px;
  }
  .work-item .work-content .title {
    margin-bottom: 18px;
  }
  .work-item .work-content p {
    margin-bottom: 28px;
  }
}

.work-slider .owl-nav [class*=owl-] {
  background: transparent;
  font-size: 24px;
  text-transform: uppercase;
  position: absolute;
  top: 50%;
  line-height: 1;
  margin-top: -12px;
  width: auto;
  margin: 0;
  color: rgba(255, 255, 255, 0.3);
  transition: all ease 0.3s;
  font-weight: 400;
}
@media (max-width: 575px) {
  .work-slider .owl-nav [class*=owl-] {
    font-size: 14px;
    top: 70px;
    padding: 0;
    color: rgba(255, 255, 255, 0.8);
  }
}
.work-slider .owl-nav [class*=owl-]::before {
  left: 0px;
  width: 100%;
  height: 24px;
  transition: all ease 0.3s;
  background: url(img/arrow.png) no-repeat center center;
  background-size: contain;
}
@media (max-width: 575px) {
  .work-slider .owl-nav [class*=owl-]::before {
    height: 14px;
  }
}
.work-slider .owl-nav [class*=owl-]:hover {
  background: transparent;
}
.work-slider .owl-nav [class*=owl-]:hover:before {
  left: 10px;
}
.work-slider .owl-nav [class*=owl-].disabled {
  opacity: 0;
}

.work-slider .owl-nav .owl-next {
  right: 0;
}

.work-slider .owl-nav .owl-prev {
  left: 0;
}

.count-slider {
  border-radius: 20px;
  background-color: #31377d;
  padding: 45px 0;
  position: relative;
  z-index: 2;
}
@media (min-width: 768px) {
  .count-slider {
    transform: translateY(95px);
    margin-top: -65px;
  }
}
@media (max-width: 767px) {
  .count-slider {
    padding: 25px 0;
  }
}
.count-slider .count-item {
  width: 25%;
  text-align: center;
  min-height: 110px;
  padding: 10px;
  align-items: center;
  justify-content: center;
  position: relative;
  border-right: 1px solid #d9deef;
  cursor: pointer;
}
.count-slider .count-item:last-child {
  border: none;
}
.count-slider .count-item::after {
  width: 100%;
  height: 100%;
}
.count-slider .count-item .title {
  margin: 0;
}
.count-slider .count-item .serial {
  font-size: 100px;
  line-height: 1;
  color: rgba(255, 255, 255, 0.08);
}
@media (max-width: 767px) {
  .count-slider .count-item {
    min-height: 80px;
  }
  .count-slider .count-item .serial {
    font-size: 60px;
  }
}
@media screen and (max-width: 510px) {
  .count-slider .count-item {
    width: 50%;
    min-height: 50px;
  }
  .count-slider .count-item:nth-child(even) {
    border: none;
  }
  .count-slider .count-item .serial {
    font-size: 50px;
  }
}

.pricing-item-2 {
  background: #31377d;
  text-align: center;
  padding: 30px 15px;
  transition: all ease 0.3s;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.pricing-item-2 .cate {
  margin-bottom: 30px;
}
.pricing-item-2 .thumb {
  height: 88px;
  margin-bottom: 32px;
}
.pricing-item-2 .thumb img {
  margin: 0 auto;
  height: 100%;
  width: auto;
}
.pricing-item-2 .title {
  font-weight: 600;
  line-height: 1;
  margin: 0;
}
.pricing-item-2 .title sup {
  top: -13px;
  font-size: 55%;
}
.pricing-item-2 .info {
  display: block;
  color: #00bc9c;
  margin-bottom: 20px;
}
.pricing-item-2 ul {
  margin-bottom: 20px;
}
.pricing-item-2 ul li {
  padding: 6px 0;
}
.pricing-item-2 .get-button i {
  transition: 0s;
}
.pricing-item-2:hover {
  border-radius: 5px;
  box-shadow: 0px 0px 30px rgba(255, 255, 255, 0.2);
  z-index: 1;
  transform: scale(1.06);
}
.pricing-item-2:hover .get-button {
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 10px 7px 0px rgba(17, 83, 252, 0.35);
  color: #ffffff;
  border-color: transparent;
}
.pricing-item-2:hover .get-button i {
  padding-left: 0;
}
@media (max-width: 767px) {
  .pricing-item-2 {
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
  }
}

.pricing-slider-wrapper {
  margin: -50px;
}
.pricing-slider-wrapper .pricing-slider {
  padding: 50px;
  overflow: hidden;
}
.pricing-slider-wrapper .pricing-slider .owl-stage-outer {
  overflow: visible;
}
.pricing-slider-wrapper .pricing-slider .owl-stage-outer .owl-item {
  visibility: hidden;
}
.pricing-slider-wrapper .pricing-slider .owl-stage-outer .owl-item:hover {
  z-index: 99;
}
.pricing-slider-wrapper .pricing-slider .owl-stage-outer .owl-item.active {
  visibility: visible;
}

.pricing-menu {
  position: relative;
  z-index: 99;
  justify-content: space-between;
  width: 235px;
  margin: 0 auto 50px;
}
.pricing-menu li {
  color: #ffffff;
  text-transform: capitalize;
  padding: 5px 10px;
  width: 50%;
  position: relative;
}
.pricing-menu li::before {
  position: absolute;
  content: "";
  width: 70px;
  height: 40px;
  background: #ee4730;
  border-radius: 20px;
  right: -43px;
  top: 50%;
  margin-top: -20px;
}
.pricing-menu li::after {
  position: absolute;
  content: "";
  width: 30px;
  height: 30px;
  background: #ffffff;
  border-radius: 15px;
  margin-top: -15px;
  z-index: 1;
  top: 50%;
  right: -38px;
  transition: all ease 0.3s;
  animation-name: fadeInLeft;
  -webkit-animation-name: fadeInLeft;
  -moz-animation-name: fadeInLeft;
  animation-duration: 0.3s;
  -webkit-animation-duration: 0.3s;
  -moz-animation-duration: 0.3s;
}
.pricing-menu li:nth-child(even) {
  text-align: right;
}
.pricing-menu li:nth-child(even)::before {
  left: -27px;
  right: auto;
}
.pricing-menu li:nth-child(even)::after {
  right: auto;
  left: -22px;
  animation-name: fadeInRight;
  -webkit-animation-name: fadeInRight;
  -moz-animation-name: fadeInRight;
  animation-duration: 0.3s;
  -webkit-animation-duration: 0.3s;
  -moz-animation-duration: 0.3s;
}
.pricing-menu li.active::after, .pricing-menu li.active::before {
  display: none;
  z-index: -9;
}

/*Feat Nav*/
.feat-nav {
  margin: -5px;
  margin-top: 50px;
}
.feat-nav a {
  width: 60px;
  height: 60px;
  line-height: 60px;
  border-radius: 50%;
  text-align: center;
  color: #3b368c;
  background: #ffffff;
  font-size: 24px;
  font-weight: 700;
  margin: 5px;
}
.feat-nav a:hover, .feat-nav a.active {
  color: #ffffff;
  background: linear-gradient(-103deg, rgb(239, 119, 76) 0%, rgb(237, 104, 79) 35%, rgb(232, 67, 81) 76%, rgb(231, 51, 81) 100%);
  box-shadow: 0px 10px 10px 0px rgba(231, 51, 81, 0.3);
}
@media (min-width: 576px) {
  .feat-nav {
    margin: -10px;
    margin-top: 80px;
  }
  .feat-nav a {
    margin: 10px;
  }
}

/*Feature Item Two Section Starts Here*/
.feature-item-2 {
  text-align: center;
  margin-bottom: 50px;
}
.feature-item-2 .feature-thumb {
  width: 160px;
  height: 160px;
  padding: 25px;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  margin: 0 auto 40px;
}
.feature-item-2 .feature-thumb img {
  max-width: 100%;
}
.feature-item-2 .feature-content .title {
  font-weight: 700;
  margin-bottom: 30px;
}
@media (max-width: 575px) {
  .feature-item-2 .feature-thumb {
    width: 100px;
    height: 100px;
    padding: 15px;
    margin-bottom: 30px;
  }
  .feature-item-2 .feature-content .title {
    margin-bottom: 20px;
  }
}

.owl-item:nth-of-type(3n + 2) .feature-item-2 .feature-thumb, div[class*=col]:nth-of-type(3n + 2) .feature-item-2 .feature-thumb {
  background: linear-gradient(0deg, rgb(209, 128, 221) 0%, rgb(121, 107, 232) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(121, 107, 232, 0.5);
}
.owl-item:nth-of-type(3n + 3) .feature-item-2 .feature-thumb, div[class*=col]:nth-of-type(3n + 3) .feature-item-2 .feature-thumb {
  background: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
  box-shadow: 4.232px 12.292px 10.56px 0.44px rgba(121, 107, 232, 0.5);
}

/*Experience Section Starts Here*/
.ex-item {
  position: relative;
  border-radius: 20px;
  border: 1px solid rgb(110, 127, 238);
  overflow: hidden;
  padding: 40px 25px;
  margin-bottom: 30px;
  text-align: center;
}
.ex-item * {
  position: relative;
  z-index: 1;
}
.ex-item::before {
  width: 100%;
  height: 100%;
  background: url(img/ex-bg.png) no-repeat center center;
  background-size: cover;
  transition: all ease 0.3s;
  opacity: 0;
}
.ex-item .ex-thumb {
  width: 100px;
  height: 100px;
  padding: 20px;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(209, 128, 221) 0%, rgb(121, 107, 232) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(121, 107, 232, 0.5);
  margin: 0 auto 30px;
}
.ex-item .ex-thumb img {
  max-height: 100%;
}
.ex-item .ex-content .title {
  margin-bottom: 20px;
}
@media (max-width: 767px) {
  .ex-item {
    max-width: 400px;
  }
}
@media screen and (max-width: 400px) {
  .ex-item .ex-thumb {
    width: 80px;
    height: 80px;
  }
}
.ex-item:hover, .ex-item.active {
  border-color: transparent;
}
.ex-item:hover::before, .ex-item.active::before {
  opacity: 1;
}
.ex-item:hover .ex-thumb, .ex-item.active .ex-thumb {
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
}
.ex-item:hover *, .ex-item.active * {
  color: #ffffff;
}

.owl-item:nth-of-type(4n + 2) .ex-item .ex-thumb, div[class*=col]:nth-of-type(4n + 2) .ex-item .ex-thumb {
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
}
.owl-item:nth-of-type(4n + 3) .ex-item .ex-thumb, div[class*=col]:nth-of-type(4n + 3) .ex-item .ex-thumb {
  background: linear-gradient(0deg, rgb(225, 53, 143) 0%, rgb(126, 108, 231) 100%);
  box-shadow: 4.232px 12.292px 10.56px 0.44px rgba(121, 107, 232, 0.5);
}
.owl-item:nth-of-type(4n + 4) .ex-item .ex-thumb, div[class*=col]:nth-of-type(4n + 4) .ex-item .ex-thumb {
  background: linear-gradient(0deg, rgb(246, 190, 101) 0%, rgb(230, 96, 58) 100%);
  box-shadow: 2.419px 9.703px 18.24px 0.76px rgba(253, 160, 133, 0.5);
}

/*Addon Section Starts Here*/
.addon-wrapper {
  width: 730px;
  height: 730px;
  border-radius: 50%;
  background: #f0f5fc;
  position: relative;
  margin: 0 auto;
}
.addon-wrapper::before {
  width: 1920px;
  height: 100%;
  background: url(img/addon-bg.png) no-repeat center center;
  background-size: contain;
  z-index: -1;
}
.addon-wrapper .addon-center {
  width: 135px;
  height: 135px;
  background: #ffffff;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
}
.addon-wrapper .addon-center::after, .addon-wrapper .addon-center::before {
  border: 1px solid #a6c1ff;
  border-radius: 50%;
}
.addon-wrapper .addon-center::after {
  width: 230px;
  height: 230px;
}
.addon-wrapper .addon-center::before {
  width: 540px;
  height: 540px;
}
.addon-wrapper .addon-center img {
  max-width: 100%;
}
.addon-wrapper .item {
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  background: #ffffff;
  border-radius: 50%;
}
.addon-wrapper .item:nth-child(2) {
  top: 60px;
  left: 50%;
  transform: translateX(-50%);
}
.addon-wrapper .item:nth-child(3) {
  top: 145px;
  left: 110px;
}
.addon-wrapper .item:nth-child(4) {
  top: 145px;
  right: 110px;
}
.addon-wrapper .item:nth-child(5) {
  top: 330px;
  left: 30px;
}
.addon-wrapper .item:nth-child(6) {
  top: 330px;
  right: 30px;
}
.addon-wrapper .item:nth-child(7) {
  bottom: 60px;
  right: 145px;
}
.addon-wrapper .item:nth-child(8) {
  bottom: 60px;
  left: 145px;
}
@media (max-width: 991px) {
  .addon-wrapper {
    width: 690px;
    height: 690px;
  }
  .addon-wrapper .addon-center::before {
    width: 510px;
    height: 510px;
  }
}
@media (max-width: 767px) {
  .addon-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: auto;
    height: auto;
    border-radius: 0;
    margin: -15px;
  }
  .addon-wrapper .addon-center {
    display: none;
  }
  .addon-wrapper .item {
    margin: 15px;
    position: relative;
    transform: translate(0) !important;
    top: unset !important;
    bottom: unset !important;
    left: unset !important;
    right: unset !important;
  }
}

/*Cost Section Starts Here*/
.cost-wrapper {
  max-width: 730px;
  margin: 0 auto;
  border: 2px solid #332d5a;
  border-radius: 20px;
  padding: 0 15px 60px;
  margin-top: 45px;
}
.cost-wrapper .section-header {
  max-width: 530px;
}
.cost-wrapper .cost-icon {
  width: 125px;
  height: 125px;
  border-radius: 50%;
  background-color: #31377d;
  margin: 0 auto;
  align-items: center;
  justify-content: center;
  transform: translateY(-60px);
}
.cost-wrapper .cost-icon .icon {
  width: 95px;
  height: 95px;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  align-items: center;
  justify-content: center;
}
.cost-wrapper .button-3 {
  text-transform: capitalize;
}
@media (max-width: 767px) {
  .cost-wrapper .button-3 {
    font-size: 14px;
    padding: 12px 25px;
  }
}
@media (max-width: 575px) {
  .cost-wrapper {
    padding-bottom: 40px;
  }
  .cost-wrapper .cost-icon {
    margin-bottom: -50px;
  }
}

/*Client Section Starts Here*/
.client-item {
  text-align: center;
  margin: 15px;
}
.client-item .client-content {
  padding: 25px 25px 30px;
  background: #3b368c;
  border-radius: 20px;
}
@media (max-width: 767px) {
  .client-item .client-content {
    padding: 25px 15px 30px;
  }
}
.client-item .client-content p {
  max-width: 210px;
  margin: 0 auto 4px;
}
@media (max-width: 767px) {
  .client-item .client-content p {
    font-size: 16px;
  }
}
.client-item .client-content .rating {
  font-size: 16px;
  color: #ffcc00;
}
.client-item .client-thumb {
  width: 74px;
  height: 74px;
  border-radius: 50%;
  overflow: hidden;
  margin: 20px auto 0;
}
.client-item .client-thumb a {
  display: block;
}
.client-item .client-thumb img {
  width: 100%;
}

@media (min-width: 992px) {
  .client-slider {
    width: 1110px;
  }
}

.button-client {
  text-transform: uppercase;
  font-weight: 600;
  border-radius: 30px;
  border: 1px solid #cac7f6;
  color: #ffffff;
  padding: 15px 35px;
}
.button-client:hover {
  color: #ffffff;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
}

@media (max-width: 991px) {
  .client-header {
    margin-bottom: 50px;
  }
}
@media (max-width: 575px) {
  .client-header {
    margin-bottom: 30px;
  }
}

/*How Section Three Starts Here*/
.how-item-3 {
  background: #31377d;
  border-radius: 20px;
  padding: 30px 18px 40px;
  text-align: center;
  margin: 0 auto 30px;
}
.how-item-3 .how-thumb {
  height: 135px;
  margin-bottom: 40px;
}
.how-item-3 .how-thumb img {
  max-height: 100%;
  width: auto;
}
.how-item-3 .how-content .title {
  margin-bottom: 25px;
}
.how-item-3 .how-content p {
  margin-bottom: 40px;
}
.how-item-3 .how-content a {
  font-weight: 400;
  text-transform: capitalize;
}

/*Faster Section Starts Here*/
.faster-content .group {
  margin: -10px;
}
.faster-content .get-button {
  margin: 10px;
  font-weight: 400;
}
.faster-content .get-button.active {
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 10px 7px 0px rgba(17, 83, 252, 0.35);
  color: #ffffff;
  border: none;
}
.faster-content .get-button.active i {
  padding-left: 5px;
}

/*Safe Section Starts Here*/
.safe-content .group {
  margin: -10px;
}
.safe-content .get-button {
  margin: 10px;
  font-weight: 400;
}
.safe-content .get-button.active {
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 10px 7px 0px rgba(17, 83, 252, 0.35);
  color: #ffffff;
  border: none;
}
.safe-content .get-button.active i {
  padding-left: 5px;
}

/*Pricing Section Three Stars Here*/
.pricing-item {
  border-radius: 20px;
  background-color: #31377d;
  text-align: center;
  padding: 30px;
  margin: 0 auto 30px;
  position: relative;
}
.pricing-item .tag {
  position: absolute;
  display: block;
  width: 160px;
  height: 50px;
  border-radius: 0 25px 25px 0;
  background: #202342;
  line-height: 50px;
  font-size: 24px;
  font-family: "Josefin Sans", sans-serif;
  text-transform: capitalize;
  color: #ffffff;
  top: 55px;
  left: -25px;
  transform: rotate(90deg);
}
@media screen and (max-width: 499px) {
  .pricing-item .tag {
    font-size: 20px;
    width: 140px;
  }
}
.pricing-item .pricing-thumb {
  height: 155px;
  margin-bottom: 15px;
}
@media screen and (max-width: 499px) {
  .pricing-item .pricing-thumb {
    height: 120px;
  }
}
.pricing-item .pricing-thumb img {
  width: auto;
  height: 100%;
}
.pricing-item .pricing-content .pricing-header {
  margin-bottom: 20px;
}
.pricing-item .pricing-content .pricing-header .title {
  font-size: 54px;
  line-height: 64px;
  margin-top: -20px;
  margin-bottom: -5px;
}
@media screen and (max-width: 499px) {
  .pricing-item .pricing-content .pricing-header .title {
    font-size: 48px;
  }
}
.pricing-item .pricing-content .pricing-header .title sup {
  top: -0.5em;
  font-weight: 400;
  font-size: 55%;
}
.pricing-item .pricing-content .pricing-header .cate {
  text-transform: capitalize;
}
.pricing-item .pricing-content ul {
  margin-bottom: 26px;
}
.pricing-item .pricing-content ul li {
  padding: 6px 0 9px;
  border-bottom: 2px dotted rgba(93, 88, 179, 0.4);
}
.pricing-item .pricing-content ul li:last-child {
  border: none;
}
.pricing-item .pricing-content .button-3 {
  padding: 10px;
  text-transform: capitalize;
  font-weight: 400;
  max-width: 250px;
  margin: 0 auto;
  border-color: #4943a0;
}

/*Recharge Section Starts Here*/
.recharge-item {
  text-align: center;
  padding: 0 15px;
  width: 25%;
  margin-bottom: 55px;
}
.recharge-item .recharge-thumb {
  margin: 0 auto 28px;
  width: 90px;
  height: 90px;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;
}
.recharge-item .recharge-thumb .anime {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 1px solid #c2c6f7;
  animation: rotate2 8s linear infinite;
  -webkit-animation: rotate2 8s linear infinite;
  -moz-animation: rotate2 8s linear infinite;
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
  -moz-animation-play-state: paused;
}
.recharge-item .recharge-thumb .anime::before, .recharge-item .recharge-thumb .anime::after {
  width: 10px;
  height: 10px;
  background-size: contain;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 1.21px 4.851px 6.72px 0.28px rgba(232, 58, 153, 0.39);
  left: 5px;
  bottom: 5px;
}
.recharge-item .recharge-thumb .anime::after {
  width: 13px;
  height: 13px;
  left: auto;
  bottom: auto;
  right: 5px;
  top: 5px;
}
.recharge-item .recharge-thumb .thumb {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(219, 218, 255, 0.5);
  background: #31377d;
}
.recharge-item .recharge-thumb .thumb img {
  width: 100%;
}
.recharge-item .recharge-content .title {
  text-transform: capitalize;
}
@media (max-width: 767px) {
  .recharge-item {
    width: 100%;
    max-width: 165px;
  }
}
.recharge-item.active .recharge-thumb .anime, .recharge-item:hover .recharge-thumb .anime {
  animation-play-state: running;
  -webkit-animation-play-state: running;
  -moz-animation-play-state: running;
}

.recharge-wrapper {
  justify-content: center;
  margin: 0 -15px -63px;
}

/*Help Section Starts Here*/
.help-item {
  border-radius: 20px;
  background: #3b368c;
  box-shadow: 0px 5px 20px 0px rgba(183, 184, 185, 0.2);
  align-items: center;
  padding: 30px 15px;
  margin: 0 auto 30px;
}
@media (max-width: 991px) {
  .help-item {
    max-width: 450px;
  }
}
.help-item .help-thumb {
  width: 80px;
}
.help-item .help-thumb img {
  width: 100%;
}
.help-item .help-content {
  width: calc(100% - 80px);
  padding-left: 30px;
}
.help-item .help-content .title {
  margin: 0;
  margin-bottom: 18px;
  text-transform: capitalize;
}
.help-item .help-content a {
  color: #00bc9c;
}
@media screen and (max-width: 400px) {
  .help-item .help-thumb {
    width: 60px;
  }
  .help-item .help-content {
    width: calc(100% - 60px);
    padding-left: 20px;
  }
}

/*Testimonial Two Section Starts Here*/
@media (max-width: 991px) {
  .testi-wrapper {
    margin-bottom: 60px;
  }
}
.testi-wrapper .testi-header {
  margin-bottom: 40px;
}
.testi-wrapper .testi-header .button-3 {
  padding: 10px 35px;
  font-size: 16px;
}

.sponsor-slider-two .sponsor-thumb {
  width: 100%;
}

.tool-wrapper {
  position: relative;
}
.tool-wrapper .owl-nav .owl-prev, .tool-wrapper .owl-nav .owl-next {
  padding: 0;
  width: 30px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  border-radius: 50%;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 10px 11px 0px rgba(49, 86, 246, 0.3);
  position: absolute;
  top: 50%;
  margin: 0;
  margin-top: -15px;
}
.tool-wrapper .owl-nav .owl-prev {
  left: -15px;
}
.tool-wrapper .owl-nav .owl-next {
  right: -15px;
}

/*App Video Section Starts Here*/
.app-video-thumb {
  position: relative;
  padding-right: 30px;
}
.app-video-thumb .video-button {
  position: absolute;
  top: 58%;
  right: 110%;
  transform: translate(-50%, -50%);
}
@media screen and (max-width: 1600px) {
  .app-video-thumb img {
    max-width: 800px;
  }
  .app-video-thumb .video-button {
    right: 89%;
  }
}
@media screen and (max-width: 1399px) {
  .app-video-thumb img {
    max-width: 700px;
  }
  .app-video-thumb .video-button {
    right: 80%;
  }
}
@media (max-width: 1199px) {
  .app-video-thumb {
    padding-right: 0;
  }
  .app-video-thumb img {
    max-width: 600px;
  }
  .app-video-thumb .video-button {
    right: 82%;
  }
}
@media (max-width: 991px) {
  .app-video-thumb {
    margin-bottom: 50px;
  }
  .app-video-thumb img {
    width: 100%;
    max-width: 100%;
  }
  .app-video-thumb .video-button {
    right: 57%;
  }
}
@media (max-width: 575px) {
  .app-video-thumb {
    margin-bottom: 40px;
  }
  .app-video-thumb .rtl {
    direction: ltr;
  }
  .app-video-thumb img {
    max-width: unset;
    min-width: 395px;
  }
  .app-video-thumb .video-button {
    right: 50%;
  }
}
@media screen and (max-width: 400px) {
  .app-video-thumb .video-button {
    right: unset;
    left: 50%;
  }
}

/*About Feature Section Starts Here*/
.about-feature-thumb {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.about-counter-item {
  margin-bottom: 40px;
  max-width: 33.33%;
  text-align: center;
}
.about-counter-item .title {
  margin-bottom: 7px;
}
.about-counter-item .title span {
  background: linear-gradient(90deg, #e2906e 0%, #e83b99 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  text-shadow: 1.871px 8.803px 7px rgba(232, 58, 153, 0.22);
}
.about-counter-item .title span:last-child {
  background: #e83b99;
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
.about-counter-item p {
  color: #ffffff;
}

.about-feature-counter-area {
  justify-content: space-between;
  padding: 20px 0;
  position: relative;
}
.about-feature-counter-area::after, .about-feature-counter-area::before {
  width: 1px;
  height: calc(100% - 40px);
  top: 0;
  background: #5338af;
}
.about-feature-counter-area::after {
  left: 27%;
}
.about-feature-counter-area::before {
  left: 70%;
}
@media screen and (max-width: 449px) {
  .about-feature-counter-area {
    padding-bottom: 0;
    justify-content: flex-start;
    margin-left: -35px;
    margin-right: -35px;
  }
  .about-feature-counter-area::before, .about-feature-counter-area::after {
    display: none;
  }
  .about-feature-counter-area .about-counter-item {
    padding: 0 35px;
    max-width: 100%;
  }
}

.feat-counter .title {
  margin-bottom: 10px;
}
@media (min-width: 768px) {
  .feat-counter .title {
    margin-bottom: 20px;
  }
}
.feat-counter .title span {
  background: linear-gradient(90deg, #e2906e 0%, #e83b99 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
  text-shadow: 0.968px 3.881px 1.92px rgba(232, 58, 153, 0.31);
}

/*Pricing Section Four Starts Here*/
.pricing-item-4 {
  text-align: center;
  border-radius: 90px;
  padding: 0 15px 60px 35px;
  position: relative;
  width: 100%;
  margin-bottom: 30px;
}
.pricing-item-4 .pricing-header {
  width: 230px;
  height: 230px;
  margin: 0 auto 38px;
  border: 2px solid #d22c48;
  border-radius: 50%;
  position: relative;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.pricing-item-4 .pricing-header * {
  width: 100%;
}
.pricing-item-4 .pricing-header::after {
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  border-radius: 50%;
  z-index: -1;
  background-color: rgba(255, 255, 255, 0.302);
}
.pricing-item-4 .pricing-header::before {
  width: 208px;
  height: 208px;
  border-radius: 50%;
  background: linear-gradient(-90deg, rgb(235, 130, 247) 0%, rgb(227, 69, 90) 99%);
}
.pricing-item-4 .pricing-header .title {
  font-weight: 600;
  text-shadow: -1.336px 6.871px 8px rgba(58, 78, 229, 0.5);
  margin: 0;
  margin-bottom: -4px;
}
.pricing-item-4 .pricing-header .title sup {
  font-size: 55%;
  font-weight: 400;
  top: -0.4em;
}
.pricing-item-4 .pricing-header span {
  text-transform: capitalize;
}
.pricing-item-4 * {
  position: relative;
  z-index: 1;
  color: #ffffff;
}
.pricing-item-4 .pricing-body .info {
  display: inline-block;
  margin: 0;
  line-height: 40px;
  padding: 5px 30px 0px;
  border-radius: 25px;
  border: 1px solid #ffffff;
  margin-bottom: 25px;
  font-weight: 600;
}
.pricing-item-4 .pricing-body ul {
  margin-bottom: 38px;
}
.pricing-item-4 .pricing-body ul li {
  padding: 0;
  margin-bottom: 13px;
}
.pricing-item-4 .pricing-body .button-3 {
  color: #ffffff;
  border-color: rgba(225, 232, 255, 0.3);
}
.pricing-item-4 .pricing-body .button-3.active {
  border: none;
}
.pricing-item-4 .pricing-body .button-3.active:hover {
  color: #ffffff;
}
.pricing-item-4::after {
  background: linear-gradient(-90deg, rgb(240, 147, 251) 0%, rgb(245, 87, 108) 100%);
  left: 20px;
  top: 115px;
  bottom: 15px;
  right: 0;
  border-radius: 90px;
}
.pricing-item-4::before {
  left: 0;
  top: 100px;
  bottom: 0;
  right: 0;
  border: 2px solid #d22c48;
  border-radius: 90px;
}
@media (max-width: 1199px) {
  .pricing-item-4 .pricing-header {
    width: 200px;
    height: 200px;
    margin: 0 auto 30px;
  }
  .pricing-item-4 .pricing-header::before {
    width: 178px;
    height: 178px;
  }
  .pricing-item-4 .pricing-header .title {
    font-size: 40px;
  }
  .pricing-item-4 .pricing-body ul li {
    font-size: 16px;
    margin-bottom: 5px;
  }
}
.pricing-item-4:nth-of-type(3n + 3) {
  padding: 0 35px 60px 15px;
}
.pricing-item-4:nth-of-type(3n + 3)::after {
  background: linear-gradient(-90deg, rgb(102, 126, 234) 0%, rgb(118, 75, 162) 100%);
  right: 20px;
  top: 115px;
  bottom: 15px;
  left: 0;
}
.pricing-item-4:nth-of-type(3n + 3)::before {
  border-color: #6976de;
}
.pricing-item-4:nth-of-type(3n + 3) .pricing-header {
  border-color: #6976de;
}
.pricing-item-4:nth-of-type(3n + 3) .pricing-header::before {
  background: linear-gradient(-90deg, rgb(102, 126, 234) 0%, rgb(118, 75, 162) 100%);
}
.pricing-item-4:nth-of-type(3n + 2)::before {
  border-color: #26a2fb;
}
.pricing-item-4:nth-of-type(3n + 2)::after {
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 1.871px 8.803px 109px 0px rgba(9, 9, 181, 0.31);
  background: linear-gradient(-90deg, rgb(104, 224, 207) 0%, rgb(32, 156, 255) 100%);
}
.pricing-item-4:nth-of-type(3n + 2) .pricing-header {
  border-color: #26a2fb;
}
.pricing-item-4:nth-of-type(3n + 2) .pricing-header::before {
  background: linear-gradient(-90deg, rgb(104, 224, 207) 0%, rgb(32, 156, 255) 100%);
}
@media (min-width: 992px) {
  .pricing-item-4:nth-of-type(3n + 2) .pricing-body .info {
    font-size: 36px;
    line-height: 50px;
    border-radius: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 991px) {
  .pricing-item-4 {
    width: 100% !important;
    padding: 0 30px 45px !important;
    max-width: 330px;
  }
  .pricing-item-4::after {
    right: 0 !important;
    left: 0 !important;
    bottom: 0 !important;
    top: 115px !important;
  }
  .pricing-item-4::before {
    top: 100px;
    display: block !important;
  }
}

.pricing-wrapper-4 {
  align-items: center;
}
.pricing-wrapper-4 .pricing-item-4 {
  width: 30%;
}
@media (max-width: 1199px) {
  .pricing-wrapper-4 .pricing-item-4 {
    width: 32.5%;
  }
}
.pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) {
  width: 40%;
}
@media (min-width: 992px) {
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) {
    padding: 0 15px 60px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header {
    width: 340px;
    height: 340px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header .title {
    font-size: 74px;
    margin-bottom: 0;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header span {
    font-size: 24px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header::before {
    width: 306px;
    height: 306px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2)::before {
    border-radius: 110px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2)::after {
    bottom: 0;
    left: 0;
    right: 0;
    top: 85px;
    border-radius: 100px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) {
    width: 35%;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header {
    width: 290px;
    height: 290px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header .title {
    font-size: 60px;
    margin-bottom: 0;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header span {
    font-size: 22px;
  }
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-header::before {
    width: 264px;
    height: 264px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) and (min-width: 992px) {
  .pricing-wrapper-4 .pricing-item-4:nth-of-type(3n + 2) .pricing-body .info {
    font-size: 30px;
    line-height: 46px;
    border-radius: 28px;
    margin-bottom: 30px;
  }
}
@media (max-width: 991px) {
  .pricing-wrapper-4 {
    margin-left: -15px;
    margin-right: -15px;
    justify-content: center;
  }
  .pricing-wrapper-4 .pricing-item-4 {
    margin-left: 15px;
    margin-right: 15px;
  }
}

div[class*=col]:nth-of-type(3n + 3) .pricing-item-4 {
  padding: 0 35px 60px 15px;
}
div[class*=col]:nth-of-type(3n + 3) .pricing-item-4::after {
  background: linear-gradient(-90deg, rgb(102, 126, 234) 0%, rgb(118, 75, 162) 100%);
  right: 20px;
  top: 115px;
  bottom: 15px;
  left: 0;
}
div[class*=col]:nth-of-type(3n + 3) .pricing-item-4::before {
  border-color: #6976de;
}
div[class*=col]:nth-of-type(3n + 3) .pricing-item-4 .pricing-header {
  border-color: #6976de;
}
div[class*=col]:nth-of-type(3n + 3) .pricing-item-4 .pricing-header::before {
  background: linear-gradient(-90deg, rgb(102, 126, 234) 0%, rgb(118, 75, 162) 100%);
}
div[class*=col]:nth-of-type(3n + 2) .pricing-item-4::before {
  border-color: #26a2fb;
}
div[class*=col]:nth-of-type(3n + 2) .pricing-item-4::after {
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(-90deg, rgb(104, 224, 207) 0%, rgb(32, 156, 255) 100%);
}
div[class*=col]:nth-of-type(3n + 2) .pricing-item-4 .pricing-header {
  border-color: #26a2fb;
}
div[class*=col]:nth-of-type(3n + 2) .pricing-item-4 .pricing-header::before {
  background: linear-gradient(-90deg, rgb(104, 224, 207) 0%, rgb(32, 156, 255) 100%);
}

/*Counter Section Four Starts Here*/
.counter-wrapper-4 {
  background: #31377d;
  border-radius: 100px;
  margin-top: -100px;
  padding: 47px 60px;
  position: relative;
  z-index: 2;
}
@media (max-width: 1199px) {
  .counter-wrapper-4 {
    margin-top: 0;
  }
}
@media (max-width: 1199px) {
  .counter-wrapper-4 {
    padding: 40px;
  }
}
@media (max-width: 991px) {
  .counter-wrapper-4 {
    padding: 40px 20px;
  }
}
@media (max-width: 767px) {
  .counter-wrapper-4 {
    padding: 0;
    margin: 0;
    background: transparent;
    border-radius: 0;
  }
}

.counter-area-4 {
  align-items: center;
  justify-content: space-between;
  margin-bottom: -30px;
}
.counter-area-4 .counter-item-4 {
  align-items: center;
  width: auto;
  max-width: 33.3333333333%;
  margin-bottom: 30px;
}
.counter-area-4 .counter-item-4 .counter-thumb {
  width: 106px;
}
.counter-area-4 .counter-item-4 .counter-thumb img {
  max-width: 100%;
}
.counter-area-4 .counter-item-4 .counter-content {
  max-width: calc(100% - 106px);
  padding-left: 25px;
}
.counter-area-4 .counter-item-4 .counter-content .title {
  margin: 0;
  font-weight: 600;
  margin-bottom: 7px;
}
@media (max-width: 1199px) {
  .counter-area-4 .counter-item-4 {
    width: 100%;
  }
  .counter-area-4 .counter-item-4 .counter-thumb {
    width: 80px;
  }
  .counter-area-4 .counter-item-4 .counter-content {
    width: calc(100% - 80px);
  }
}
@media (max-width: 991px) and (min-width: 768px) {
  .counter-area-4 .counter-item-4 {
    width: 100%;
  }
  .counter-area-4 .counter-item-4 .counter-thumb {
    width: 50px;
  }
  .counter-area-4 .counter-item-4 .counter-content {
    padding-left: 15px;
    width: calc(100% - 50px);
  }
  .counter-area-4 .counter-item-4 .counter-content .title {
    font-size: 36px;
    line-height: 1;
  }
  .counter-area-4 .counter-item-4 .counter-content p {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .counter-area-4 {
    margin-left: -20px;
    margin-right: -20px;
  }
  .counter-area-4 .counter-item-4 {
    max-width: 260px;
    padding-left: 20px;
    padding-right: 20px;
  }
  .counter-area-4 .counter-item-4 .counter-thumb {
    width: 70px;
  }
  .counter-area-4 .counter-item-4 .counter-content {
    width: calc(100% - 70px);
  }
}

/*Page Header Starts Here*/
.page-header {
  padding: 233px 0 170px;
  position: relative;
  overflow: hidden;
}
@media (max-width: 767px) {
  .page-header {
    padding: 180px 0 100px;
  }
}
.page-header.single-header {
  height: 599px;
  padding: 0;
}
.page-header.single-header::before {
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgb(33, 2, 174) 0%, rgb(232, 58, 153) 100%);
  opacity: 0.7;
}
.page-header.single-header.blog-single-header {
  height: 450px;
}
@media (max-width: 991px) {
  .page-header.single-header {
    height: 550px;
  }
  .page-header.single-header.blog-single-header {
    height: 400px;
  }
}
@media (max-width: 767px) {
  .page-header.single-header {
    height: 450px;
  }
  .page-header.single-header.blog-single-header {
    height: 300px;
  }
}
.page-header .page-header-content.hide {
  display: none;
  z-index: -9;
  opacity: 0;
  visibility: hidden;
}

.page-header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}
.page-header-content .title {
  font-size: 66px;
  line-height: 70px;
  margin: 0;
}
@media (max-width: 767px) {
  .page-header-content .title {
    font-size: 48px;
    line-height: 55px;
  }
}
@media (max-width: 575px) {
  .page-header-content .title {
    font-size: 42px;
    line-height: 50px;
  }
}
.page-header-content .breadcrumb {
  justify-content: center;
  background: transparent;
  padding: 0;
}
.page-header-content .breadcrumb li {
  text-transform: capitalize;
  padding: 5px;
}
.page-header-content .breadcrumb li a {
  align-items: center;
}
.page-header-content .breadcrumb li a::after {
  font-family: "Flaticon";
  font-weight: 500;
  margin-left: 10px;
  content: "\f104";
}
.page-header-content .breadcrumb li a:hover {
  color: #05c3de;
}

/*About Section Starts Here*/
.counter-area-5 {
  align-items: center;
  justify-content: space-between;
  margin: 0 -15px;
  position: relative;
}
.counter-area-5::after, .counter-area-5::before {
  width: 1px;
  height: 100%;
  top: 0;
  background: #d8daf3;
}
.counter-area-5::after {
  left: 67%;
}
.counter-area-5::before {
  left: 32%;
}
@media screen and (max-width: 767px) {
  .counter-area-5 {
    justify-content: flex-start;
  }
  .counter-area-5::before, .counter-area-5::after {
    display: none;
  }
}
.counter-area-5 .counter-item-5 {
  align-items: center;
  width: auto;
  max-width: 33.3333333333%;
  padding: 15px;
}
.counter-area-5 .counter-item-5 .counter-thumb {
  width: 65px;
}
.counter-area-5 .counter-item-5 .counter-thumb img {
  max-width: 100%;
}
.counter-area-5 .counter-item-5 .counter-content {
  width: calc(100% - 65px);
  padding-left: 15px;
}
.counter-area-5 .counter-item-5 .counter-content .title {
  font-weight: 600;
  margin-bottom: 12px;
}
@media (max-width: 767px) {
  .counter-area-5 .counter-item-5 {
    width: 100%;
    max-width: 180px;
  }
  .counter-area-5 .counter-item-5 .counter-thumb {
    width: 55px;
  }
  .counter-area-5 .counter-item-5 .counter-content {
    width: calc(100% - 55px);
    padding-left: 10px;
  }
  .counter-area-5 .counter-item-5 .counter-content .title {
    font-size: 30px;
  }
  .counter-area-5 .counter-item-5 .counter-content p {
    font-size: 16px;
  }
}

@media (max-width: 1199px) {
  .about-thumb {
    max-width: 600px;
    margin-bottom: 60px;
  }
  .about-thumb img {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .about-thumb {
    margin-bottom: 50px;
  }
}
@media (max-width: 575px) {
  .about-thumb {
    margin-bottom: 40px;
  }
}

/*Team Section Starts Here*/
.team-item {
  margin-bottom: 40px;
}
.team-item .team-thumb {
  position: relative;
  transition: all ease 0.5s;
  padding-right: 14px;
  padding-bottom: 14px;
  margin-bottom: 40px;
}
.team-item .team-thumb::before {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  background: url(img/team_odd.png) no-repeat center center;
  background-size: cover;
  transition: all ease 0.5s;
}
.team-item .team-thumb::after {
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  border-radius: 50%;
  border: 1px solid #2229b2;
  z-index: -1;
}
.team-item .team-thumb img {
  border-radius: 50%;
  width: 100%;
}
.team-item .team-content {
  text-align: center;
}
.team-item .team-content .title a:hover {
  color: #ee4730;
}
.team-item .team-content .info {
  display: block;
  margin-bottom: 20px;
}
.team-item .team-content .linkedin {
  width: 50px;
  height: 50px;
  line-height: 50px;
  border-radius: 50%;
  margin: 5px 10px;
  margin-bottom: 0;
  border: 1px solid #cbcaee;
  font-size: 24px;
  color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ffffff;
}
.team-item:hover .team-thumb {
  padding: 0;
}
.team-item:hover .team-thumb::before {
  transform: rotate(90deg);
}
@media (max-width: 767px) {
  .team-item .team-thumb {
    max-width: 350px;
    margin: 0 auto 25px;
  }
  .team-item .team-content .info {
    margin-bottom: 10px;
  }
}

div[class*=col]:nth-child(even) .team-item .team-thumb {
  padding: 0;
  padding-left: 15px;
  padding-top: 15px;
}
div[class*=col]:nth-child(even) .team-item .team-thumb::before {
  transform: rotate(180deg);
}
div[class*=col]:nth-child(even) .team-item:hover .team-thumb {
  padding: 0;
}
div[class*=col]:nth-child(even) .team-item:hover .team-thumb::before {
  transform: rotate(90deg);
}

.load-more {
  text-align: center;
}
.load-more a {
  font-size: 30px;
  line-height: 1;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}
.load-more a:hover {
  color: #ee4730;
}

/*Sponsor Slider Four Starts Here*/
.sponsor-slider-4 .sponsor-thumb {
  max-width: 100%;
  height: 45px;
  align-items: center;
  justify-content: center;
}
.sponsor-slider-4 .sponsor-thumb img {
  width: auto;
  height: 100%;
}

/*History Section Starts Here*/
.history-slider .owl-stage-outer {
  padding-top: 20px;
}

.history-item {
  text-align: center;
}
.history-item .history-content {
  margin: 0 auto 52px;
  position: relative;
}
.history-item .history-content .content {
  padding: 35px 15px;
  max-width: 220px;
  border-radius: 20px;
  box-shadow: 3.629px 14.554px 30.72px 1.28px rgba(24, 37, 163, 0.2);
  background: #ffffff;
  margin: 0 auto 52px;
  transition: all ease 0.3s;
  opacity: 0;
  visibility: hidden;
  background: #3b368c;
}
.history-item .history-content::before {
  height: 28px;
  width: 2px;
  background: #2c18b4;
  left: 50%;
  top: calc(100% + 7px);
  transition: all ease 0.3s;
  opacity: 0;
  visibility: hidden;
}
.history-item .history-content::after {
  width: 100%;
  height: 2px;
  background: #2c18b4;
  top: calc(100% + 52px);
  left: 0;
}
.history-item .history-content .title {
  font-size: 18px;
  margin: 0;
  margin-bottom: 4px;
  font-weight: 600;
}
.history-item .history-content p {
  margin-top: 0;
  font-size: 14px;
}
.history-item .history-thumb {
  padding-top: 23px;
  position: relative;
  cursor: pointer;
}

.owl-item.center .history-item .history-content::before, .owl-item.center .history-item .history-content .content {
  opacity: 1;
  visibility: visible;
}

.anime-item-2 {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ee4730;
  z-index: 1;
  top: 0;
  left: 50%;
}
.anime-item-2::after, .anime-item-2::before {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #ee4730;
  box-shadow: 3.694px -4.728px 38px 0px rgba(17, 65, 189, 0.52);
  margin-left: -5px;
  margin-top: -5px;
  cursor: pointer;
}
.anime-item-2::before {
  animation: inner-ripple 2000ms linear infinite;
}
.anime-item-2::after {
  animation: outer-ripple 2000ms linear infinite;
}

/*Chart Section Starts Here*/
.chart-header .title {
  font-weight: 600;
}

.chart-counter-item {
  margin-bottom: 32px;
}
@media (min-width: 992px) {
  .chart-counter-item:last-child {
    margin-bottom: 0;
  }
}
.chart-counter-item p {
  margin-bottom: 34px;
}
.chart-counter-item .title {
  font-weight: 600;
  text-shadow: 1.247px 5.869px 3px rgba(232, 58, 153, 0.3);
}
.chart-counter-item .title span {
  background: linear-gradient(-90deg, #e83b99 0%, #e28e6f 100%);
  -webkit-text-fill-color: transparent;
  -moz-text-fill-color: transparent;
  -webkit-background-clip: text;
  -moz-background-clip: text;
  background-clip: text;
}
@media (max-width: 767px) {
  .chart-counter-item p {
    margin-bottom: 25px;
  }
}

#chartContainer {
  height: 400px;
}
@media (max-width: 767px) {
  #chartContainer {
    height: 300px;
  }
}

/*Team Single Starts Here*/
.team-wrapper {
  align-items: flex-start;
}
.team-wrapper .team-thumb {
  width: 100%;
  max-width: 350px;
  text-align: center;
  margin-bottom: 35px;
}
.team-wrapper .team-thumb img {
  width: 100%;
}
.team-wrapper .team-content {
  width: calc(100% - 350px);
  padding-left: 40px;
}
.team-wrapper .team-content .author .title {
  text-transform: capitalize;
  margin-bottom: -5px;
}
.team-wrapper .team-content .author .info {
  margin-bottom: 26px;
}
.team-wrapper .team-content .content .s-item {
  margin-bottom: 74px;
}
.team-wrapper .team-content .content .s-item .subtitle {
  font-size: 28px;
  text-transform: capitalize;
  margin-top: -9px;
  margin-bottom: 28px;
}
.team-wrapper .team-content .content .s-item p {
  margin-bottom: 37px;
}
@media (max-width: 991px) {
  .team-wrapper .team-content .content .s-item {
    margin-bottom: 54px;
  }
}
@media (max-width: 767px) {
  .team-wrapper .team-content .content .s-item {
    margin-bottom: 44px;
  }
}
.team-wrapper .team-content .content .s-item:last-child {
  margin-bottom: 0;
}
@media (min-width: 768px) and (max-width: 991px) {
  .team-wrapper {
    padding-left: 30px;
    padding-right: 30px;
  }
  .team-wrapper .team-thumb {
    max-width: 200px;
  }
  .team-wrapper .team-content {
    width: calc(100% - 200px);
    padding-left: 30px;
  }
  .team-wrapper .team-content .author .title {
    margin-bottom: 0;
  }
}
@media (max-width: 767px) {
  .team-wrapper {
    padding-left: 30px;
    padding-right: 30px;
  }
  .team-wrapper .team-content {
    width: 100%;
    padding: 0;
    text-align: center;
  }
  .team-wrapper .team-content .item {
    width: auto;
    margin-left: auto;
    margin-right: auto;
  }
  .team-wrapper .team-content .team-icons {
    justify-content: center;
    margin-top: -5px;
    margin-left: -5px;
    margin-right: -5px;
  }
  .team-wrapper .team-content .team-icons li {
    padding: 5px;
  }
  .team-wrapper .team-content .team-icons li a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 20px;
  }
  .team-wrapper .team-content .content .subtitle {
    font-size: 24px;
    margin-bottom: 20px;
  }
}
@media (max-width: 767px) and (max-width: 575px) {
  .team-wrapper .team-content .content .subtitle {
    margin-bottom: 20px;
  }
}
@media (max-width: 767px) {
  .team-wrapper .team-content .content p {
    margin-bottom: 30px;
  }
  .team-wrapper .team-content .author .title {
    margin-bottom: 0;
  }
  .team-wrapper .team-thumb {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 30px;
  }
}
@media (max-width: 575px) {
  .team-wrapper {
    padding-left: 15px;
    padding-right: 15px;
  }
  .team-wrapper .team-thumb {
    margin-bottom: 25px;
  }
  .team-wrapper .team-content .author .title {
    margin-bottom: 5px;
  }
  .team-wrapper .team-content .author .info {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 22px;
  }
  .team-wrapper .team-content .content p {
    font-size: 16px;
  }
}

.team-con-wrapper {
  padding: 24px 40px;
  border-top: 1px solid #c6d0e3;
  border-bottom: 1px solid #c6d0e3;
  position: relative;
  margin-bottom: 60px;
}
.team-con-wrapper::before {
  height: calc(100% - 30px);
  width: 1px;
  background: #c6d0e3;
  left: 50%;
  top: 15px;
}
@media (max-width: 991px) {
  .team-con-wrapper::before {
    display: none;
  }
}
@media (max-width: 991px) {
  .team-con-wrapper {
    margin-bottom: 50px;
  }
}

.team-con-area {
  justify-content: space-between;
  margin-bottom: -30px;
  align-items: center;
}
.team-con-area .item {
  margin-bottom: 26px;
}
.team-con-area .item .item-thumb {
  width: 42px;
}
.team-con-area .item .item-thumb img {
  max-width: 100%;
}
.team-con-area .item .item-content {
  width: calc(100% - 42px);
  padding-left: 13px;
}
.team-con-area .item .item-content span, .team-con-area .item .item-content a {
  font-size: 14px;
  line-height: 22px;
  display: block;
}
.team-con-area .item .item-content a {
  color: #ee4730;
}
.team-con-area .item .item-content .up {
  margin-bottom: 2px;
}
@media (max-width: 991px) {
  .team-con-area .item {
    margin-bottom: 20px;
    width: 100%;
  }
}
.team-con-area .team-icons {
  margin: -10px;
  margin-bottom: 22.5px;
}
@media (max-width: 991px) {
  .team-con-area .team-icons {
    width: 100%;
  }
}
.team-con-area .team-icons li {
  padding: 10px;
}
.team-con-area .team-icons li a {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  border-radius: 50%;
  margin: 0;
  margin-bottom: 0;
  border: 1px solid #cbcaee;
  font-size: 24px;
  color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ffffff;
}

.progress-area {
  margin-bottom: -40px;
}
.progress-area .progress-item {
  margin-bottom: 40px;
}
.progress-area .progress-item .progress-label {
  justify-content: space-between;
  padding-bottom: 6px;
  text-transform: capitalize;
}
.progress-area .progress-item .progress-label span {
  display: block;
  margin-bottom: 10px;
}
@media (max-width: 575px) {
  .progress-area .progress-item .progress-label span {
    font-size: 16px;
  }
}
.progress-area .progress-item .progress {
  height: 14px;
  border-radius: 7px;
  background: rgb(255, 233, 207);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(134, 134, 231, 0.5);
}
.progress-area .progress-item .progress .progress-bar {
  border-radius: 7px;
  background: linear-gradient(0deg, rgb(0, 129, 234) 0%, rgb(127, 255, 224) 100%);
}
.progress-area .progress-item:nth-of-type(3n + 2) .progress .progress-bar {
  background: linear-gradient(0deg, rgb(77, 57, 255) 0%, rgb(245, 144, 255) 100%);
}
.progress-area .progress-item:nth-of-type(3n + 3) .progress .progress-bar {
  background: linear-gradient(0deg, rgb(255, 128, 0) 0%, rgb(255, 188, 72) 100%);
}

/*Partner Section Starts Here*/
.partner-item {
  justify-content: center;
  background: #31377d;
  padding: 60px 20px;
  margin: 0 auto 30px;
  text-align: center;
}
.partner-item a {
  display: block;
}
.partner-item img {
  max-height: 115px;
  max-width: 100%;
}

/*User Counter Section Starts Here*/
.user-counter-wrapper {
  position: relative;
  justify-content: center;
  align-items: center;
}
@media (min-width: 768px) {
  .user-counter-wrapper {
    min-height: 490px;
  }
}
.user-counter-wrapper .user-counter-bg {
  width: 100%;
  height: 100%;
  min-width: 690px;
}
.user-counter-wrapper * {
  position: relative;
  z-index: 1;
}
.user-counter-wrapper .section-header .title {
  font-weight: 600;
}
@media (min-width: 768px) {
  .user-counter-wrapper .section-header .title {
    font-size: 76px;
  }
}

/*Review Section Starts Here*/
.review-item {
  margin: 0 auto 30px;
}
@media (max-width: 575px) {
  .review-item {
    max-width: 320px;
  }
}
.review-item .review-content {
  padding: 40px 30px 70px;
  border-radius: 20px;
  background: linear-gradient(16deg, rgb(108, 1, 214) 0%, rgb(113, 45, 229) 35%, rgb(118, 88, 244) 100%);
  color: #ffffff;
  position: relative;
}
.review-item .review-content::before, .review-item .review-content::after {
  position: absolute;
  content: "";
}
.review-item .review-content::before {
  width: 79px;
  height: 63px;
  background: url(img/review-before.png) no-repeat center center;
  background-size: contain;
  top: 30px;
  left: 30px;
}
.review-item .review-content::after {
  width: 79px;
  height: 63px;
  background: url(img/review-after.png) no-repeat center center;
  background-size: contain;
  right: 30px;
  bottom: 30px;
}
.review-item .review-thumb {
  margin: -30px 30px 0;
  padding: 15px 30px;
  background: #31377d;
  border-radius: 10px;
  position: relative;
  z-index: 1;
  align-items: center;
  justify-content: space-between;
}
.review-item .review-thumb .review-author-thumb {
  width: 55px;
  padding: 6px;
  border-radius: 50%;
  border: 1px solid #daccff;
  overflow: hidden;
}
.review-item .review-thumb .review-author-thumb a {
  display: block;
  border-radius: 50%;
  border: 1px solid #6e0fd1;
  overflow: hidden;
}
.review-item .review-thumb .review-author-thumb img {
  width: 100%;
}
.review-item .review-thumb .review-author-content {
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 55px);
  padding-left: 18px;
}
.review-item .review-thumb .review-author-content .left {
  width: calc(100% - 20px);
}
.review-item .review-thumb .review-author-content .left .title {
  font-size: 18px;
  margin: 0;
  margin-bottom: -4px;
}
.review-item .review-thumb .review-author-content .left span {
  font-size: 14px;
}
.review-item .review-thumb .review-author-content .icon {
  width: 20px;
  text-align: right;
}
.review-item .review-thumb .review-author-content .icon a {
  font-size: 14px;
  color: #bdb9f0;
}
.review-item .review-thumb .review-author-content .icon a:hover {
  color: #ee4730;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .review-item .review-thumb {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media screen and (max-width: 380px) {
  .review-item .review-thumb {
    padding-left: 15px;
    padding-right: 15px;
  }
}

/*Comunity Section Starts Here*/
.comunity-bg {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 160px;
}
@media (min-width: 992px) {
  .comunity-bg {
    bottom: 360px;
  }
}

.comunity-wrapper {
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  position: relative;
  border-radius: 10px;
  background: #31377d;
  overflow: hidden;
}
@media (min-width: 576px) {
  .comunity-wrapper {
    padding: 100px 0;
  }
}
@media (min-width: 992px) {
  .comunity-wrapper {
    min-height: 490px;
  }
}
.comunity-wrapper .button-3 {
  padding: 10px 35px;
}
@media (max-width: 575px) {
  .comunity-wrapper .button-3 {
    font-size: 16px;
  }
}
.comunity-wrapper .buttons {
  position: relative;
  z-index: 1;
}

.comunity-asking {
  border-radius: 10px;
  background: #3b368c;
  padding: 65px 35px;
}
.comunity-asking .help-item {
  background: #202342;
}
@media (max-width: 575px) {
  .comunity-asking {
    padding: 0;
    background: transparent;
  }
}
.comunity-asking img {
  width: 100%;
}

.comunity-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  animation: comunity 40s linear infinite;
  -webkit-animation: comunity 40s linear infinite;
  -moz-animation: comunity 40s linear infinite;
}
.comunity-area.two {
  top: -100%;
}
.comunity-area.three {
  top: 100%;
}

@keyframes comunity {
  0% {
    transform: translateY(100%);
    opacity: 1;
  }
  50% {
    transform: translateY(0);
    opacity: 0.9;
  }
  99.99% {
    transform: translateY(-100%);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
.comunity-item {
  position: absolute;
}
.comunity-item img {
  width: 100%;
  border-radius: 50%;
}
.comunity-item:nth-child(1) {
  width: 141px;
  height: 141px;
  opacity: 0.8;
  left: calc(50% + 255px);
  top: 310px;
}
.comunity-item:nth-child(2) {
  width: 63px;
  height: 63px;
  opacity: 0.4;
  left: calc(50% + 465px);
  top: 260px;
}
.comunity-item:nth-child(3) {
  width: 64px;
  height: 64px;
  opacity: 0.5;
  left: calc(50% + 53px);
  top: 340px;
}
.comunity-item:nth-child(4) {
  width: 42px;
  height: 42px;
  opacity: 0.5;
  left: calc(50% + 177px);
  top: 290px;
}
.comunity-item:nth-child(5) {
  width: 65px;
  height: 65px;
  opacity: 0.5;
  left: calc(50% + 280px);
  top: 212px;
}
.comunity-item:nth-child(6) {
  width: 42px;
  height: 42px;
  opacity: 0.5;
  left: calc(50% + 390px);
  top: 158px;
}
.comunity-item:nth-child(7) {
  width: 65px;
  height: 65px;
  opacity: 0.5;
  left: calc(50% + 460px);
  top: 70px;
}
.comunity-item:nth-child(8) {
  width: 65px;
  height: 65px;
  opacity: 0.3;
  right: calc(50% + 120px);
  top: 387px;
}
.comunity-item:nth-child(9) {
  width: 65px;
  height: 65px;
  opacity: 0.5;
  right: calc(50% + 345px);
  top: 362px;
}
.comunity-item:nth-child(10) {
  width: 105px;
  height: 105px;
  opacity: 0.5;
  right: calc(50% + 210px);
  top: 265px;
}
.comunity-item:nth-child(11) {
  width: 45px;
  height: 45px;
  opacity: 0.3;
  right: calc(50% + 87px);
  top: 278px;
}
.comunity-item:nth-child(12) {
  width: 68px;
  height: 68px;
  opacity: 0.2;
  left: calc(50% - 15px);
  top: 210px;
}
.comunity-item:nth-child(13) {
  width: 68px;
  height: 68px;
  opacity: 0.65;
  left: calc(50% + 170px);
  top: 90px;
}
.comunity-item:nth-child(14) {
  width: 44px;
  height: 44px;
  opacity: 0.5;
  left: calc(50% + 325px);
  top: 10px;
}
.comunity-item:nth-child(15) {
  width: 43px;
  height: 43px;
  opacity: 0.5;
  left: calc(50% + 65px);
  top: 70px;
}
.comunity-item:nth-child(16) {
  width: 49px;
  height: 49px;
  opacity: 0.4;
  right: calc(50% + 475px);
  top: 296px;
}
.comunity-item:nth-child(17) {
  width: 49px;
  height: 49px;
  opacity: 0.4;
  right: calc(50% + 390px);
  top: 256px;
}
.comunity-item:nth-child(18) {
  width: 45px;
  height: 45px;
  opacity: 0.5;
  right: calc(50% + 265px);
  top: 205px;
}
.comunity-item:nth-child(19) {
  width: 75px;
  height: 75px;
  opacity: 0.2;
  right: calc(50% + 213px);
  top: 133px;
}
.comunity-item:nth-child(20) {
  width: 110px;
  height: 110px;
  opacity: 0.3;
  right: calc(50% - 16px);
  top: 10px;
}
.comunity-item:nth-child(21) {
  width: 146px;
  height: 146px;
  opacity: 0.3;
  right: calc(50% + 375px);
  top: 86px;
}
.comunity-item:nth-child(22) {
  width: 61px;
  height: 61px;
  opacity: 0.3;
  right: calc(50% + 280px);
  top: 55px;
}

/*Faq Section Two Starts Here*/
.sticky-menu {
  position: sticky;
  top: 120px;
}

.faq-menu {
  padding: 60px 30px;
  border-radius: 20px;
}
.faq-menu ul li .nav-link {
  padding: 8px 0;
  color: #ffffff;
  position: relative;
  transition: all 0.5s ease-in-out;
}
.faq-menu ul li .nav-link::before {
  position: absolute;
  content: "\f105";
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
  left: 12px;
  top: 10px;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.faq-menu ul li .nav-link:hover, .faq-menu ul li .nav-link.active {
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.2);
  padding-left: 30px;
  transition: all 0.5s ease-in-out;
}
.faq-menu ul li .nav-link:hover::before, .faq-menu ul li .nav-link.active::before {
  opacity: 1;
}
@media (max-width: 575px) {
  .faq-menu {
    padding: 40px 30px;
  }
}
@media (max-width: 991px) {
  .faq-menu {
    max-width: 400px;
    margin: 0 auto 30px;
  }
}

.faq-video {
  padding: 10px;
  background: #31377d;
  border-radius: 20px;
  text-align: center;
}
@media (max-width: 991px) {
  .faq-video {
    max-width: 400px;
    margin: 0 auto;
  }
}
.faq-video .video-area {
  border-radius: 20px;
  position: relative;
  display: block;
  overflow: hidden;
}
.faq-video .video-area img {
  width: 100%;
}
.faq-video .video-area .video-button-2 i {
  background: #ff8a00;
  border-color: #ff8a00;
}
.faq-video .title {
  margin: 0;
  padding: 20px 0 10px;
}

.faq--wrapper {
  margin-bottom: 53px;
}
.faq--wrapper .main-title {
  font-weight: 700;
  margin: 0;
  margin-bottom: 26px;
}
.faq--wrapper:last-child {
  margin-bottom: 0;
}
@media (max-width: 991px) {
  .faq--wrapper {
    margin-bottom: 43px;
  }
  .faq--wrapper .main-title {
    margin-bottom: 16px;
  }
}

.faq--item {
  background: #31377d;
  padding: 26px 30px;
  position: relative;
  border-radius: 10px;
  margin-bottom: 20px;
}
.faq--item:last-child {
  margin-bottom: 0;
}
.faq--item .faq-title {
  cursor: pointer;
}
.faq--item .faq-title .title {
  margin: 0;
  padding-right: 15px;
  font-weight: 600;
  font-size: 22px;
}
.faq--item .faq-title .icon {
  width: 12px;
  height: 12px;
  border-top: 1px solid #ffffff;
  border-left: 1px solid #ffffff;
  position: absolute;
  right: 30px;
  top: 35px;
  transform: rotate(135deg);
  transition: all ease 0.3s;
}
.faq--item .faq-content {
  padding-top: 30px;
  padding-bottom: 10px;
  display: none;
}
.faq--item.open .icon {
  transform: rotate(225deg);
}
.faq--item.active .faq-content {
  display: block;
}
@media (max-width: 575px) {
  .faq--item {
    padding-left: 15px;
    padding-right: 15px;
  }
  .faq--item .faq-title .title {
    font-size: 20px;
  }
  .faq--item .faq-title .icon {
    left: 15px;
    top: 32px;
  }
}

.mt-70 {
  margin-top: 70px;
}

/*Privacy Section Starts Here*/
.privacy-item {
  margin-bottom: 62px;
}
.privacy-item .title {
  margin: 0;
  margin-bottom: 31px;
}
.privacy-item p {
  margin-bottom: 40px;
}
@media (max-width: 991px) {
  .privacy-item {
    margin-bottom: 40px;
  }
  .privacy-item .title {
    font-size: 28px;
    margin-bottom: 20px;
  }
  .privacy-item p {
    margin-bottom: 30px;
  }
}
.privacy-item:last-child {
  margin-bottom: 0;
}

.page-left-thumb {
  position: absolute;
  bottom: 0;
  right: calc(50% + 400px);
  z-index: 1;
}

/*Coming Soon Section Starts Here*/
.coming-soon {
  min-height: 100vh;
  padding: 120px 0;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.coming-wrapper .title {
  margin: 0;
  margin-bottom: 30px;
  color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ffffff;
  font-size: 50px;
}
@media (min-width: 576px) {
  .coming-wrapper .title {
    font-size: 60px;
  }
}
@media (min-width: 768px) {
  .coming-wrapper .title {
    font-size: 86px;
  }
}

.countdown {
  justify-content: center;
  margin: -10px;
}
.countdown li {
  margin: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.051);
  padding: 40px 15px;
}
.countdown li .c-title {
  font-size: 76px;
  color: rgb(58, 209, 229);
  text-shadow: 2.419px 9.703px 12.48px rgba(58, 209, 229, 0.5);
  line-height: 1;
  margin: 0;
  margin-bottom: 18px;
}
.countdown li p {
  font-family: "Josefin Sans", sans-serif;
  font-size: 24px;
  line-height: 1.4;
  color: #ffffff;
  text-transform: uppercase;
  font-weight: 300;
}
@media (min-width: 992px) {
  .countdown {
    margin: -22px;
  }
  .countdown li {
    min-width: 150px;
    margin: 22px;
  }
}
@media (max-width: 991px) {
  .countdown li {
    min-width: 130px;
  }
  .countdown li .c-title {
    font-size: 50px;
  }
  .countdown li p {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .countdown li {
    padding: 30px 15px;
    min-width: 130px;
  }
}

.notify-form {
  max-width: 540px;
  margin: 60px auto 0;
  position: relative;
}
.notify-form input {
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.102);
  color: #ffffff;
  padding-left: 40px;
  padding-right: 160px;
  height: 70px;
}
.notify-form input::-moz-placeholder {
  color: #c1a8ec;
}
.notify-form input::placeholder {
  color: #c1a8ec;
}
.notify-form button {
  height: 50px;
  border-radius: 10px;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  border: none;
  color: #ffffff;
  position: absolute;
  top: 10px;
  right: 10px;
  width: auto;
  padding: 0 20px;
}
@media screen and (max-width: 449px) {
  .notify-form {
    text-align: left;
  }
  .notify-form button {
    position: relative;
    margin-right: auto;
    right: unset;
    top: unset;
  }
  .notify-form input {
    padding: 0 20px;
    margin-bottom: 10px;
  }
}

/*Account Section Starts Here*/
.account-section {
  padding: 30px 0;
  min-height: 100vh;
  justify-content: center;
}

.account-title {
  margin-bottom: 30px;
  position: relative;
}
@media (max-width: 767px) {
  .account-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .account-title > * {
    max-width: 50%;
  }
  .account-title .logo {
    display: block;
  }
  .account-title .logo img {
    max-width: 100%;
  }
}
.account-title .back-home {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  text-align: left;
  display: inline-flex;
  align-items: center;
}
@media (min-width: 768px) {
  .account-title .back-home {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
.account-title .back-home i {
  margin-right: 10px;
  font-size: 24px;
  line-height: 1;
  color: transparent;
  -webkit-text-stroke-width: 1px;
  -webkit-text-stroke-color: #ffffff;
}
.account-title .back-home span {
  line-height: 1;
}
@media (min-width: 768px) {
  .account-title {
    margin-bottom: 80px;
  }
}

.account-wrapper {
  max-width: 540px;
  margin: 0 auto;
  border-radius: 30px;
  background: #31377d;
  padding: 60px 0;
}
@media (max-width: 575px) {
  .account-wrapper {
    font-size: 16px;
  }
}
.account-wrapper.top-gap {
  margin-top: 100px;
}
@media (min-width: 1200px) {
  .account-wrapper.top-gap {
    margin-top: 130px;
  }
}

.account-header {
  padding: 0 40px 40px;
  text-align: center;
}
@media (max-width: 575px) {
  .account-header {
    padding: 0 15px 30px;
  }
}
.account-header span a {
  color: #ee4730;
  font-weight: 600;
  text-transform: capitalize;
}
.account-header .title {
  margin-bottom: 23px;
}
.account-header .sign-in-with {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 40px;
  color: #bdb9f0;
  border: 1px solid #aa9dd0;
  border-radius: 27px;
}
.account-header .sign-in-with img {
  width: 24px;
  margin-right: 10px;
}
@media (max-width: 575px) {
  .account-header .sign-in-with {
    padding: 12px 30px;
  }
  .account-header .sign-in-with img {
    width: 20px;
  }
}

.or {
  position: relative;
  text-align: center;
}
.or::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 0;
  height: 1px;
  width: 100%;
  background: #dddce7;
}
.or span {
  display: inline-block;
  padding: 0 12px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  background: #31377d;
  position: relative;
  z-index: 1;
}

.account-body {
  padding: 10px 40px 0;
  text-align: center;
}
@media (max-width: 575px) {
  .account-body {
    padding: 0 15px 0;
  }
}
.account-body span a {
  color: #ee4730;
  font-weight: 600;
  text-transform: capitalize;
}

.account-form {
  text-align: left;
}
.account-form .form-group {
  margin-bottom: 13px;
}
.account-form .form-group label {
  font-size: 18px;
  font-weight: 600;
  font-family: "Josefin Sans", sans-serif;
  color: #ffffff;
}
.account-form .form-group input {
  border: 1px solid rgba(59, 54, 140, 0.1);
  border-radius: 5px;
  background-color: rgb(246, 246, 250);
  padding: 0 30px;
  height: 60px;
  color: #3b368c;
}
.account-form .form-group button {
  border: none;
  border-radius: 10px;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  width: auto;
  padding: 0 45px;
  color: #ffffff;
  margin-top: 20px;
}
@media (min-width: 576px) {
  .account-form .form-group button {
    margin-top: 40px;
  }
}

.mt-15 {
  margin-top: 15px;
}

.mt-40 {
  margin-top: 40px;
}

.sign-in-recovery {
  font-size: 16px;
  margin-top: 10px;
  display: block;
}
.sign-in-recovery a {
  color: #0403c4;
  font-weight: 600;
  text-transform: capitalize;
}

.form-head {
  text-align: center;
  max-width: 345px;
  margin: 0 auto 40px;
  font-size: 16px;
}
@media (min-width: 576px) {
  .form-head {
    margin-bottom: 60px;
  }
}

/*Error Section Starts Here*/
.error-section {
  position: relative;
  padding: 100px 0;
  min-height: 100vh;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.error-section .man1, .error-section .man2 {
  position: absolute;
  height: calc(100% - 160px);
  top: 50%;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%);
}
.error-section .man1 img, .error-section .man2 img {
  max-height: 100%;
}
.error-section .man1 {
  left: calc(50% + 230px);
}
.error-section .man2 {
  right: calc(50% + 230px);
}

.error-wrapper {
  text-align: center;
  max-width: 550px;
  margin: 0 auto;
}
.error-wrapper .title {
  margin: 0;
  font-size: 230px;
  line-height: 1;
  text-shadow: 1.21px 4.851px 13.44px rgba(25, 83, 200, 0.37);
  color: #ffffff;
  margin-bottom: 60px;
}
.error-wrapper .subtitle {
  color: #ffffff;
  margin-bottom: 20px;
}
@media (max-width: 575px) {
  .error-wrapper .title {
    font-size: 130px;
    margin-bottom: 40px;
  }
  .error-wrapper .subtitle {
    margin-bottom: 10px;
  }
}

/*Feature Tab Section Starts Here*/
.feature-tab-menu {
  justify-content: center;
  padding: 7px 75px;
  background: url(img/feature-tab.png) no-repeat center center;
  background-size: cover;
  text-align: center;
  border-radius: 20px;
  margin-bottom: 70px;
}
.feature-tab-menu li {
  width: 33.3333333333%;
  padding: 10px 15px;
}
.feature-tab-menu li a, .feature-tab-menu li .nav-link {
  display: block;
  border-radius: 10px;
  color: #ffffff;
  padding: 10px;
}
.feature-tab-menu li a.active, .feature-tab-menu li .nav-link.active {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.2);
  box-shadow: 3.936px 4.528px 6px 0px rgba(0, 0, 0, 0.004);
}
@media (max-width: 1199px) {
  .feature-tab-menu {
    padding: 12px 25px;
  }
}
@media (max-width: 767px) {
  .feature-tab-menu {
    padding: 12px;
    margin-bottom: 50px;
  }
  .feature-tab-menu li {
    width: auto;
    padding: 7px 10px;
  }
  .feature-tab-menu li a, .feature-tab-menu li .nav-link {
    padding: 10px 20px;
    font-size: 14px;
  }
}

.feature-tab-header {
  margin-bottom: 40px;
}
.feature-tab-header .title {
  margin-bottom: 20px;
  text-transform: capitalize;
}
@media (min-width: 768px) {
  .feature-tab-header {
    margin-bottom: 60px;
  }
}

.cola-item {
  margin-bottom: 70px;
  align-items: center;
  flex-wrap: wrap-reverse;
  margin-left: -15px;
  margin-right: -15px;
}
@media (max-width: 767px) {
  .cola-item {
    margin-bottom: 50px;
  }
}
.cola-item:last-child {
  margin-bottom: 0;
}
.cola-item:nth-child(even) {
  flex-direction: row-reverse;
}
.cola-item:nth-child(odd) .cola-thumb {
  text-align: right;
}
.cola-item .cola-thumb img {
  max-width: 100%;
}
@media (max-width: 767px) {
  .cola-item .cola-thumb {
    margin-bottom: 20px;
  }
}
.cola-item .cola-content .thumb {
  width: 108px;
  margin-bottom: 32px;
}
.cola-item .cola-content .thumb img {
  width: 100%;
}
.cola-item .cola-content .cate {
  color: #ff8a00;
  font-weight: 600;
  font-family: "Josefin Sans", sans-serif;
  text-transform: capitalize;
  display: block;
  margin-bottom: 27px;
}
.cola-item .cola-content .title {
  font-weight: 700;
  margin-bottom: 10px;
}
.cola-item .cola-content ul li {
  padding: 0;
  margin-bottom: 10px;
}
.cola-item .cola-content ul li:last-child {
  margin-bottom: 0;
}
.cola-item .cola-content ul li::before {
  content: "\f00c";
  font-weight: 600;
  font-family: "Font Awesome 5 Free";
  margin-right: 7px;
  font-size: 14px;
}
@media (max-width: 991px) {
  .cola-item .cola-content .thumb {
    width: 70px;
    margin-bottom: 22px;
  }
  .cola-item .cola-content .cate {
    margin-bottom: 17px;
  }
}
@media (max-width: 767px) {
  .cola-item .cola-content ul li {
    font-size: 16px;
    margin-bottom: 5px;
  }
  .cola-item .cola-content ul li:last-child {
    margin-bottom: 0;
  }
}

/*Single Pricing Section Starts Here*/
.single-pricing {
  margin-top: -420px;
  position: relative;
  z-index: 1;
}
@media (max-width: 767px) {
  .single-pricing {
    margin-top: -270px;
  }
}

.show-feature {
  font-size: 24px;
  color: #1153fc;
  text-transform: capitalize;
  font-family: "Josefin Sans", sans-serif;
  text-decoration: underline;
  font-weight: 700;
}

/*Estimate Plan Section Starts Here*/
.invest-range-area .main-amount {
  margin-bottom: 30px;
  position: relative;
}
@media (min-width: 576px) {
  .invest-range-area .main-amount {
    margin-bottom: 40px;
  }
}
.invest-range-area .main-amount::after {
  content: "\f0d7";
  font-family: "Font Awesome 5 Free";
  font-weight: 600;
  color: #dc3893;
  bottom: -26px;
  font-size: 16px;
  left: 50%;
  position: absolute;
  transform: translateX(-50%);
}
.invest-range-area .main-amount input {
  background: transparent;
  border: none;
  height: 20px;
  color: #33406a;
  font-size: 16px;
  font-weight: 500;
  padding: 0;
  text-align: center;
  color: #dc3893;
}

.user-range-area {
  align-items: center;
  justify-content: center;
  justify-content: space-between;
  text-align: center;
}
.user-range-area .min-user, .user-range-area .max-user {
  width: 100px;
}
.user-range-area .min-user .title, .user-range-area .max-user .title {
  font-weight: 700;
  margin-bottom: -3px;
}
@media (min-width: 768px) {
  .user-range-area .min-user .title, .user-range-area .max-user .title {
    font-size: 28px;
  }
}
.user-range-area .min-user span, .user-range-area .max-user span {
  display: block;
}
.user-range-area .invest-amount {
  width: calc(100% - 220px);
}
.user-range-area .invest-amount .invest-range-slider {
  margin: 0 auto;
}
@media (max-width: 575px) {
  .user-range-area .min-user, .user-range-area .max-user {
    width: 40px;
    margin-top: 30px;
  }
  .user-range-area .min-user .title, .user-range-area .max-user .title {
    margin-bottom: -10px;
  }
  .user-range-area .invest-amount {
    width: 100%;
    order: -1;
  }
}

/*Contact Section Starts Here*/
.contact-section {
  margin-top: -535px;
  position: relative;
  z-index: 9;
}
@media (max-width: 991px) {
  .contact-section {
    margin-top: -470px;
  }
}
@media (max-width: 767px) {
  .contact-section {
    margin-top: -350px;
  }
}

.contact-wrapper {
  border-radius: 5px;
  background: #31377d;
  box-shadow: 0px 11px 19.2px 0.8px rgba(66, 58, 232, 0.1);
  padding: 65px 40px 0;
  margin-bottom: 80px;
}
@media (max-width: 575px) {
  .contact-wrapper {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (min-width: 768px) {
  .contact-wrapper {
    margin-bottom: 100px;
  }
}
@media (min-width: 992px) {
  .contact-wrapper {
    margin-bottom: 0;
  }
}

.contact-form .form-group {
  margin-bottom: 23px;
}
.contact-form .form-group label {
  font-size: 18px;
  font-weight: 600;
  font-family: "Josefin Sans", sans-serif;
  color: #ffffff;
}
.contact-form .form-group input {
  border: 1px solid rgba(59, 54, 140, 0.1);
  border-radius: 5px;
  background-color: rgb(246, 246, 250);
  padding: 0 30px;
  height: 60px;
  color: #3b368c;
}
.contact-form .form-group input[type=submit] {
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  width: auto;
  padding: 0 50px;
  text-transform: uppercase;
  font-weight: 600;
  margin: 0 auto;
  display: block;
  border: none;
  border-radius: 30px;
  box-shadow: 0 10px 30px rgba(118, 93, 234, 0.502);
  color: #ffffff;
}
.contact-form .form-group button {
  border: none;
  border-radius: 10px;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  width: auto;
  padding: 0 45px;
  color: #ffffff;
  margin-top: 20px;
}
@media (min-width: 576px) {
  .contact-form .form-group button {
    margin-top: 40px;
  }
}
.contact-form .form-group textarea {
  border: 1px solid rgba(59, 54, 140, 0.1);
  border-radius: 5px;
  background-color: rgb(246, 246, 250);
  padding: 30px;
  height: 140px;
}
.contact-form .form-group .form-check {
  align-items: center;
  padding: 0;
  padding-top: 10px;
  padding-bottom: 15px;
}
.contact-form .form-group .form-check input {
  width: 22px;
  height: 22px;
  border-radius: 4px;
  background: transparent;
}
.contact-form .form-group .form-check label {
  margin: 0;
  padding-left: 10px;
  width: calc(100% - 22px);
  font-size: 16px;
  font-weight: 400;
  font-family: "Open Sans", sans-serif;
}
.contact-form .form-group:last-child {
  transform: translateY(23px);
}
.contact-form .form-group input, .contact-form .form-group textarea {
  padding: 15px;
}
@media (max-width: 575px) {
  .contact-form .form-group input::-moz-placeholder, .contact-form .form-group textarea::-moz-placeholder {
    font-size: 14px;
  }
  .contact-form .form-group input::placeholder, .contact-form .form-group textarea::placeholder {
    font-size: 14px;
  }
}

.contact-content .man {
  margin-top: 20px;
  margin-bottom: 65px;
}
.contact-content .section-header .title {
  font-weight: 600;
}
.contact-content .section-header p {
  margin-bottom: 35px;
}
.contact-content .section-header a {
  color: #bdb9f0;
  padding-left: 25px;
  position: relative;
}
.contact-content .section-header a i {
  margin-left: 5px;
}
.contact-content .section-header a::before {
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  opacity: 0.502;
  width: 50px;
  height: 50px;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  transition: all ease 0.3s;
}
.contact-content .section-header a:hover::before {
  left: calc(100% - 30px);
}

.contact-area {
  margin-bottom: -40px;
}

.contact-item {
  align-items: center;
  margin-bottom: 40px;
}
.contact-item .contact-thumb {
  width: 78px;
}
.contact-item .contact-thumb img {
  max-width: 100%;
}
.contact-item .contact-contact {
  width: calc(100% - 78px);
  padding-left: 30px;
}
.contact-item .contact-contact .subtitle {
  margin: 0;
  margin-bottom: 5px;
}
.contact-item .contact-contact a {
  color: #bdb9f0;
}
.contact-item .contact-contact p {
  margin: 0;
}

/*Do Section Starts Here*/
.do-item {
  text-align: center;
  background: url(img/do-bg.png) no-repeat center center;
  background-size: cover;
  padding: 70px 30px 60px;
  border-radius: 20px;
  margin-bottom: 30px;
}
.do-item .title {
  margin-bottom: 22px;
}
.do-item p {
  max-width: 220px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 22px;
}
.do-item a {
  width: 48px;
  height: 48px;
  line-height: 48px;
  border-radius: 50%;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 1.21px 4.851px 13.44px 0.56px rgba(232, 58, 153, 0.37);
}
@media (max-width: 575px) {
  .do-item {
    padding: 60px 15px 50px;
  }
  .do-item .title {
    margin-bottom: 13px;
  }
}

/*Map Section Starts Here*/
.map-section {
  position: relative;
}
.map-section::before {
  background: #31377d;
  top: 85px;
  left: 0;
  right: 0;
  bottom: 210px;
}
@media (max-width: 575px) {
  .map-section::before {
    top: 45px;
    bottom: 100px;
  }
}

.maps-wrapper {
  position: relative;
  border-radius: 20px;
  margin-bottom: 40px;
  z-index: 1;
}
.maps-wrapper::before {
  bottom: -40px;
  top: 40px;
  right: -50px;
  left: 50px;
  border-radius: 20px;
  background: linear-gradient(0deg, rgb(131, 195, 250) 0%, rgb(102, 183, 255) 47%, rgb(98, 144, 251) 75%, rgb(94, 105, 246) 100%);
  box-shadow: 5.806px 23.287px 30.72px 1.28px rgba(94, 105, 246, 0.5);
}
.maps-wrapper .maps {
  height: 490px;
  border-radius: 20px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .maps-wrapper::before {
    bottom: -30px;
    top: 30px;
    right: -30px;
    left: 30px;
  }
}
@media (max-width: 575px) {
  .maps-wrapper {
    margin-bottom: 20px;
  }
  .maps-wrapper::before {
    bottom: -20px;
    top: 20px;
    right: 0;
    left: 0;
  }
}

/*Extra CSS Starts Here*/
.pricing-range .pricing-header .select-bar, .pricing-range .pricing-header .cate, .amount-area .item .info {
  color: #3b368c;
}

/*RTL Feature Section Starts Here*/
.swap-area {
  position: fixed;
  left: 100%;
  top: 50%;
  transform: translate(-100%, -50%);
  padding: 10px;
  background: #fff;
  z-index: 9999;
  min-width: 150px;
  transition: all ease 0.3s;
}

.swap-area .swap-item a {
  line-height: 30px;
  display: block;
  background: #fff;
  font-size: 16px;
  text-transform: capitalize;
  padding: 5px 23px;
  color: #00353b;
}

.swap-area .swap-item a:hover {
  background: #ff8a00;
  color: #00353b;
}

@media (max-width: 575px) {
  .swap-area .swap-item a {
    font-size: 14px;
  }
}
.swap-area .chorka {
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: #fff;
  position: absolute;
  bottom: 100%;
  right: 100%;
  cursor: pointer;
  transition: all ease 0.3s;
  transform: translateX(100%);
}

.swap-area .chorka img {
  width: 40px;
  animation: rotate 5s linear infinite;
  -webkit-animation: rotate 5s linear infinite;
  -moz-animation: rotate 5s linear infinite;
}

.swap-area.active {
  transform: translate(0, -50%);
}

.swap-area.active .chorka {
  transform: translateX(0);
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/*Special Item Starts Here*/
.special-area {
  margin: -15px;
  justify-content: center;
}

.special-item {
  width: 25%;
  padding: 15px;
}

.special-item .special-content {
  padding: 30px;
  background: #001232;
  transition: all ease 0.3s;
}

.special-item .special-content .title {
  margin-bottom: 20px;
}

.special-item .special-content.active, .special-item .special-content:hover {
  background: #ff8a00;
}

.special-item .special-content.active *, .special-item .special-content:hover * {
  color: #000;
}

@media (max-width: 991px) {
  .special-item {
    width: 100%;
    max-width: 270px;
  }
}
/*All Animations Starts Here*/
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes rev-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}
@keyframes righTLeft {
  0% {
    right: 0;
    transform: translateX(100%);
  }
  100% {
    right: 100%;
    transform: translateX(0);
  }
}
@keyframes lefTRight {
  0% {
    left: 0;
    transform: translateX(-100%);
  }
  100% {
    left: 100%;
    transform: translateX(0);
  }
}
@keyframes zigZag {
  0% {
    transform: rotate(0deg) translate(-10px, -10px);
  }
  20% {
    transform: rotate(360deg) translate(-100px, -100px);
  }
  40% {
    transform: rotate(0deg) translate(100px, 100px);
  }
  60% {
    transform: rotate(360deg) translate(10px, 10px);
  }
  80% {
    transform: rotate(0deg) translate(70px, 70px);
  }
  100% {
    transform: rotate(360deg) translate(-10px, -10px);
  }
}
@keyframes outer-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    -webkit-filter: alpha(opacity=50);
  }
  80% {
    transform: scale(1.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
  }
  100% {
    transform: scale(3.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(3.5);
    -moz-transform: scale(3.5);
    -ms-transform: scale(3.5);
    -o-transform: scale(3.5);
  }
}
/* inner ripple */
@keyframes inner-ripple {
  0% {
    transform: scale(1);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
  }
  30% {
    transform: scale(2);
    filter: alpha(opacity=50);
    opacity: 0.5;
    -webkit-transform: scale(2);
    -moz-transform: scale(2);
    -ms-transform: scale(2);
    -o-transform: scale(2);
  }
  100% {
    transform: scale(2.5);
    filter: alpha(opacity=0);
    opacity: 0;
    -webkit-transform: scale(2.5);
    -moz-transform: scale(2.5);
    -ms-transform: scale(2.5);
    -o-transform: scale(2.5);
  }
}
@keyframes rotate2 {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(1440deg);
  }
}
@keyframes zigZag2 {
  0% {
    transform: rotate(0deg) translate(-10px, -10px);
  }
  20% {
    transform: rotate(360deg) translate(-10px, -10px);
  }
  40% {
    transform: rotate(0deg) translate(10px, 10px);
  }
  60% {
    transform: rotate(360deg) translate(10px, 10px);
  }
  80% {
    transform: rotate(0deg) translate(10px, 10px);
  }
  100% {
    transform: rotate(360deg) translate(-10px, -10px);
  }
}
@keyframes upDown {
  0% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(20px);
  }
}
/*All Animation Ends Here*/
.blog-author {
  padding: 30px;
  border-radius: 10px;
}
.blog-author .author-thumb {
  width: 94px;
  height: 94px;
  padding: 5px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.5);
}
.blog-author .author-thumb a {
  display: block;
  border-radius: 50%;
  overflow: hidden;
}
.blog-author .author-thumb a img {
  width: 100%;
}
.blog-author .author-content {
  width: calc(100% - 94px);
  padding-left: 30px;
}
.blog-author .author-content .title {
  margin: 0;
  text-transform: uppercase;
  margin-bottom: 18px;
}
@media (max-width: 575px) {
  .blog-author {
    padding-left: 15px;
    padding-right: 15px;
  }
  .blog-author .author-thumb {
    margin: 0 auto 20px;
  }
  .blog-author .author-content {
    padding-left: 0;
    width: 100%;
    text-align: center;
  }
}

.blog-comment {
  margin-top: 40px;
}
@media (min-width: 768px) {
  .blog-comment {
    margin-top: 50px;
  }
}
@media (min-width: 992px) {
  .blog-comment {
    margin-top: 80px;
  }
}
.blog-comment > .title {
  text-transform: uppercase;
  padding-bottom: 15px;
  font-size: 22px;
  font-weight: 700;
}
@media (min-width: 576px) {
  .blog-comment > .title {
    font-size: 28px;
    padding-bottom: 23px;
  }
}

.comment-area li {
  padding: 0;
}
.comment-area li .blog-item {
  border-top: 1px solid #bccaea;
  padding: 21px 0;
  align-items: center;
}
.comment-area li .blog-item .blog-thumb {
  width: 65px;
  height: 65px;
  padding: 5px;
  border: 1px solid #bccaea;
  border-radius: 50%;
}
.comment-area li .blog-item .blog-thumb a {
  display: block;
  border-radius: 50%;
  overflow: hidden;
}
.comment-area li .blog-item .blog-thumb a img {
  width: 100%;
}
.comment-area li .blog-item .blog-thumb-info {
  width: 160px;
  padding-left: 30px;
}
.comment-area li .blog-item .blog-thumb-info span {
  margin-bottom: 10px;
  display: block;
  color: #ee4730;
  font-size: 14px;
}
.comment-area li .blog-item .blog-thumb-info .title {
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
}
.comment-area li .blog-item .blog-content {
  width: calc(100% - 225px);
  padding-left: 30px;
}
.comment-area li .blog-item .blog-content p {
  margin: 0 !important;
  font-size: 16px;
  line-height: 26px;
}
@media (max-width: 575px) {
  .comment-area li .blog-item .blog-thumb {
    margin-bottom: 10px;
  }
  .comment-area li .blog-item .blog-thumb-info {
    width: 100%;
    padding: 0;
    margin-bottom: 10px;
  }
  .comment-area li .blog-item .blog-thumb-info span {
    margin-bottom: 7px;
  }
  .comment-area li .blog-item .blog-content {
    padding: 0;
    width: 100%;
  }
}
.comment-area li ul {
  padding-left: 20px;
}
@media (min-width: 576px) {
  .comment-area li ul {
    padding-left: 40px;
  }
}
@media (min-width: 768px) {
  .comment-area li ul {
    padding-left: 95px;
  }
}

/*Leave-Comment Starts*/
.leave-comment {
  margin-top: 20px;
}
@media (min-width: 768px) {
  .leave-comment {
    margin-top: 35px;
  }
}
@media (min-width: 992px) {
  .leave-comment {
    margin-top: 55px;
  }
}
.leave-comment .title {
  text-transform: uppercase;
  padding-bottom: 15px;
  font-size: 22px;
}
@media (min-width: 576px) {
  .leave-comment .title {
    font-size: 28px;
  }
}

.blog-form {
  margin-bottom: -30px;
  font-size: 16px;
}
@media (max-width: 575px) {
  .blog-form {
    font-size: 14px;
  }
}
.blog-form .form-group {
  margin-bottom: 30px;
}
.blog-form .form-group input {
  height: 52px;
  border: none;
  border-bottom: 1px solid #bccaea;
  background: transparent;
  border-radius: 0;
  padding-left: 0;
  color: #ffffff;
}
.blog-form .form-group input::-moz-placeholder {
  color: #bdb9f0;
}
.blog-form .form-group input::placeholder {
  color: #bdb9f0;
}
.blog-form .form-group input:focus {
  border-color: #e883ae;
}
.blog-form .form-group input[type=submit] {
  text-transform: uppercase;
  font-weight: 600;
  width: auto;
  padding: 0 50px;
  border-radius: 26px;
  color: #ffffff;
  background: linear-gradient(-30deg, #c165dd 0%, #5c27fe 100%);
  box-shadow: 0px 10px 15px 0px rgba(59, 55, 188, 0.5);
  border: none;
}
.blog-form .form-group textarea {
  padding: 0;
  height: 200px;
  background: transparent;
  border: none;
  border-bottom: 1px solid #bccaea;
  border-radius: 0;
  color: #ffffff;
}
.blog-form .form-group textarea:focus {
  border-color: #e883ae;
}
.blog-form .form-group textarea::-moz-placeholder {
  color: #bdb9f0;
}
.blog-form .form-group textarea::placeholder {
  color: #bdb9f0;
}
@media (max-width: 575px) {
  .blog-form {
    margin-bottom: -20px;
  }
  .blog-form .form-group {
    margin-bottom: 20px;
  }
  .blog-form .form-group input {
    height: 45px;
  }
  .blog-form .form-group textarea {
    height: 150px;
  }
}

.post-item {
  margin: 0 auto 40px;
  overflow: hidden;
}
.post-item .post-thumb a {
  display: block;
}
.post-item .post-thumb img {
  width: 100%;
}
.post-item .post-content {
  background: #31377d;
  position: relative;
  z-index: 1;
  margin: -30px 4px 0;
  padding: 30px 20px;
}
.post-item .post-content .title {
  margin: 0;
  margin-bottom: 20px;
}
.post-item .post-content p {
  margin-bottom: 15px;
}
.post-item .post-content .read {
  font-size: 16px;
  color: #ee4730;
}
@media (min-width: 768px) {
  .post-item .post-content {
    margin: -80px 30px 0;
    padding: 34px 30px;
  }
  .post-item .post-content .title {
    font-size: 35px;
    margin-bottom: 25px;
  }
  .post-item .post-content p {
    margin-bottom: 30px;
  }
}
.post-item.style-two {
  background: #31377d;
  padding: 20px;
  position: relative;
}
.post-item.style-two::before, .post-item.style-two::after {
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: calc(100% + 40px);
  -webkit-clip-path: polygon(0 0, 100% 0%, 100% 74%, 0% 100%);
  clip-path: polygon(0 0, 100% 0%, 100% 74%, 0% 100%);
}
.post-item.style-two::after {
  height: 140px;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
}
.post-item.style-two::before {
  height: 160px;
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  opacity: 0.502;
}
@media (min-width: 768px) {
  .post-item.style-two::before {
    height: 335px;
  }
  .post-item.style-two::after {
    height: 300px;
  }
}
.post-item.style-two .post-thumb {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}
.post-item.style-two .post-content {
  margin: 0;
  padding: 0;
  position: relative;
  z-index: 1;
  background: transparent;
}
@media (min-width: 768px) {
  .post-item.style-two {
    padding: 40px;
  }
  .post-item.style-two .post-thumb {
    margin-bottom: 32px;
  }
}

.pagination-area {
  font-size: 18px;
  margin-left: 0 -15px;
  overflow: hidden;
}
.pagination-area a {
  color: #7a89bb;
  margin: 0 15px;
}
.pagination-area a i {
  font-size: 14px;
}
.pagination-area a:hover {
  color: #ee4730;
}
@media (max-width: 575px) {
  .pagination-area a span {
    display: none;
  }
}
.pagination-area a.active {
  width: 42px;
  height: 42px;
  text-align: center;
  line-height: 42px;
  background: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.35);
  border-radius: 50%;
  color: #ffffff;
}
.pagination-area a:first-child span {
  margin-left: 3px;
}
.pagination-area a:last-child span {
  margin-right: 3px;
}
@media (max-width: 575px) {
  .pagination-area {
    margin: 0 -10px;
  }
  .pagination-area a {
    margin: 0 10px;
  }
}

/*BLog Details Section Starts Here*/
.post-details {
  border-radius: 20px;
  background: #31377d;
  margin-bottom: 60px;
}
.post-details .post-inner {
  padding: 50px 60px 40px;
}
.post-details .post-header .meta-post {
  font-size: 16px;
  margin-right: -15px;
  margin-bottom: 36px;
}
.post-details .post-header .meta-post a {
  color: #bdb9f0;
  margin-right: 15px;
  margin-bottom: 5px;
}
.post-details .post-header .title {
  margin-bottom: 36px;
}
@media (min-width: 576px) {
  .post-details .post-header .title {
    font-weight: 700;
  }
}
@media (min-width: 992px) {
  .post-details .post-header .title {
    font-size: 46px;
    line-height: 55px;
  }
}
.post-details .post-content {
  align-items: start;
}
.post-details .post-content .entry-content {
  width: calc(100% - 50px);
  padding-left: 30px;
}
.post-details .post-content .entry-content p {
  margin-bottom: 40px;
}
.post-details .post-content .entry-content img {
  margin-bottom: 44px;
  width: 100%;
}
.post-details .post-content .entry-meta {
  justify-content: center;
  width: 50px;
}
.post-details .post-content .entry-meta .thumb {
  width: 50px;
  height: 50px;
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  margin-bottom: 14px;
}
.post-details .post-content .entry-meta .thumb::before {
  height: 100%;
  width: 100%;
  background: linear-gradient(-90deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
}
.post-details .post-content .entry-meta .thumb img {
  position: relative;
  z-index: 1;
  width: 46px;
  border-radius: 50%;
}
.post-details .post-content .entry-meta .comment {
  text-align: center;
  display: block;
  margin-bottom: 15px;
}
.post-details .post-content .entry-meta .comment i {
  width: 30px;
  height: 30px;
  line-height: 30px;
  display: block;
  border: 1px solid rgba(31, 53, 92, 0.1);
  border-radius: 50%;
  text-align: center;
  font-size: 14px;
  color: #ee4730;
  margin: 0 auto;
}
.post-details .post-content .entry-meta .comment span {
  display: block;
  font-size: 16px;
  color: #bdb9f0;
}
@media (max-width: 767px) {
  .post-details {
    margin-bottom: 40px;
  }
  .post-details .post-inner {
    padding: 30px 15px 30px;
  }
  .post-details .post-header .meta-post {
    margin-bottom: 20px;
  }
  .post-details .post-header .title {
    margin-bottom: 25px;
  }
  .post-details .post-content p {
    font-size: 16px;
    line-height: 26px;
  }
  .post-details .post-content .entry-content p {
    margin-bottom: 30px;
  }
  .post-details .post-content .entry-content img {
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .post-details .post-header .title {
    margin-bottom: 20px;
  }
  .post-details .post-content .entry-content, .post-details .post-content .entry-meta {
    width: 100%;
    padding: 0;
  }
  .post-details .post-content .entry-meta {
    justify-content: flex-start;
    margin-bottom: 15px;
  }
  .post-details .post-content .entry-meta .comment {
    display: flex;
    align-items: center;
    margin-left: 20px;
  }
  .post-details .post-content .entry-meta .comment span {
    margin-left: 5px;
  }
}
.post-details .tags-area {
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
  border-top: 1px solid #bccaea;
  padding: 30px 30px 15px;
}
@media (max-width: 767px) {
  .post-details .tags-area {
    padding: 20px 15px 5px;
  }
}
.post-details .tags-area .tags {
  font-size: 16px;
  margin-bottom: 15px;
}
.post-details .tags-area .tags span {
  font-size: 18px;
  font-weight: 500;
  color: #ffffff;
  font-weight: 600;
}
.post-details .tags-area .tags a {
  color: #bdb9f0;
}
.post-details .tags-area .tags a::after {
  content: ",";
}
.post-details .tags-area .tags a:last-child::after {
  display: none;
}
.post-details .tags-area .social-icons {
  margin: -5px;
  margin-bottom: 10px;
  justify-content: flex-start;
}
.post-details .tags-area .social-icons li {
  padding: 5px;
}
.post-details .tags-area .social-icons li a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-color: #bccaea;
  color: rgba(189, 185, 240, 0.8);
}
.post-details .tags-area .social-icons li a.active, .post-details .tags-area .social-icons li a:hover {
  border: none;
  color: #ffffff;
  background: linear-gradient(-30deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
  box-shadow: 0px 3px 7px 0px rgba(0, 0, 0, 0.35);
}

.mb-70 {
  margin-bottom: 70px;
}

.blog-single-section {
  margin-top: -280px;
  position: relative;
  z-index: 1;
}
@media (max-width: 767px) {
  .blog-single-section {
    margin-top: -160px;
  }
}

.scrollToTop {
  width: 35px;
  height: 35px;
  line-height: 35px;
  color: #ffffff;
  z-index: 999;
  bottom: 30px;
  right: 30px;
  position: fixed;
  border-radius: 5px;
  transform: translateY(150px);
  background: #05c3de;
  text-align: center;
  font-size: 16px;
}
.scrollToTop:hover {
  color: #ffffff;
}
.scrollToTop.active {
  transform: translateY(0);
  animation: bounceInDown 2s;
  -webkit-animation: bounceInDown 2s;
  -moz-animation: bounceInDown 2s;
}
.scrollToTop img {
  width: 100%;
}

.transparent-button {
  color: #ffffff;
  border: 1px solid #c087fa;
  border-radius: 25px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 10px 25px;
}
.transparent-button:hover {
  color: #ee4730;
  background-color: #ffffff;
  border-color: #ee4730;
}
@media (max-width: 575px) {
  .transparent-button {
    font-size: 16px;
  }
}

.button-2 {
  color: #ffffff;
  border-radius: 25px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 10px 25px;
  background: #735ff1;
}
.button-2:hover {
  color: #ee4730;
  background-color: #ffffff;
  border-color: #ee4730;
}
.button-2 i {
  margin-left: 5px;
}
@media (max-width: 575px) {
  .button-2 {
    font-size: 16px;
  }
}

.app-button-group {
  margin-bottom: -20px;
  margin-right: -30px;
}
.app-button-group .app-button {
  display: block;
  margin-bottom: 20px;
  width: 50%;
  margin-right: 30px;
  border-radius: 50px;
  max-width: 200px;
}
.app-button-group .app-button:hover {
  box-shadow: 0px 17px 24px 0px rgba(18, 83, 252, 0.51);
}
.app-button-group .app-button img {
  width: 100%;
}
@media screen and (max-width: 399px) {
  .app-button-group .app-button {
    width: 100%;
    max-width: 190px;
  }
}

.button-3 {
  text-transform: uppercase;
  font-weight: 600;
  border-radius: 30px;
  border: 1px solid #cac7f6;
  color: #ffffff;
  padding: 15px 35px;
}
.button-3.active, .button-3:hover {
  color: #ffffff;
  border-color: transparent;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  box-shadow: 0px 17px 24px 0px rgba(18, 83, 252, 0.51);
}
.button-3.active:hover {
  background: transparent;
  border: 1px solid #cac7f6;
  color: #ffffff;
  box-shadow: none;
}
.button-3 i {
  margin-left: 5px;
}
.button-3.long-light {
  padding: 10px 30px;
  font-weight: 400;
  text-transform: capitalize;
  font-size: 16px;
}

.button-group {
  margin: -10px;
}
.button-group a {
  margin: 10px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .button-group a {
    padding: 10px 20px;
    font-size: 16px;
  }
}

.custom-button {
  color: #ffffff;
  border: 1px solid #ee4730;
  border-radius: 25px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 10px 35px;
  font-size: 16px;
  text-transform: uppercase;
  background: #ee4730;
}
.custom-button:hover {
  color: #ee4730;
  background-color: #ffffff;
  border-color: #ee4730;
}
@media (max-width: 575px) {
  .custom-button {
    font-size: 16px;
  }
}

.button-4 {
  text-transform: uppercase;
  font-weight: 600;
  border-radius: 30px;
  color: #ffffff;
  padding: 15px 45px;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
}
.button-4:hover {
  color: #ffffff;
  box-shadow: 0px 17px 24px 0px rgba(18, 83, 252, 0.51);
}
.button-4.active {
  background: transparent;
  border: 1px solid #6e5dc6;
}

.play-button {
  display: inline-flex;
  align-items: center;
}
.play-button i {
  font-size: 60px;
  line-height: 60px;
}
.play-button span {
  text-transform: capitalize;
  font-weight: 600;
  padding-left: 20px;
}
.play-button .video-button i {
  line-height: 50px;
  font-size: 20px;
}

.banner-button-group {
  align-items: center;
  margin: -10px;
}
.banner-button-group a {
  margin: 10px;
}
@media (min-width: 576px) {
  .banner-button-group {
    margin: -15px;
  }
  .banner-button-group a {
    margin: 15px;
  }
}

.video-button {
  width: 51px;
  height: 51px;
  line-height: 51px;
  text-align: center;
  border-radius: 50%;
  background: #ff8a00;
  position: relative;
  color: #ffffff;
  margin: 0 auto;
}
.video-button i {
  margin-left: 5px;
}
.video-button::after, .video-button::before {
  animation: video 25s linear infinite;
  -webkit-animation: video 25s linear infinite;
  -moz-animation: video 25s linear infinite;
  transition: all ease 0.3s;
}
.video-button::before {
  background: rgba(255, 138, 0, 0.161);
  width: 100px;
  height: 100px;
  animation-delay: 0.5s;
  -webkit-animation-delay: 0.5s;
  -moz-animation-delay: 0.5s;
  border-radius: 33.33% 50%;
}
.video-button::after {
  background: rgba(255, 138, 0, 0.302);
  height: 73px;
  width: 73px;
}

@keyframes video {
  0% {
    border-radius: 33.33% 50%;
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    border-radius: 50% 33.33%;
    transform: translate(-50%, -50%) rotate(1800deg);
  }
}
@media (min-width: 576px) {
  .large-button {
    padding: 15px 55px;
    border-radius: 30px;
  }
}

.get-button {
  font-size: 16px;
  font-weight: 600;
  text-transform: capitalize;
  padding: 11px 34px;
  color: #ffffff;
  border: 1px solid #d4cff4;
  border-radius: 25px;
}
.get-button i {
  margin-left: 14px;
  transition: all ease 0.3s;
}
.get-button:hover {
  box-shadow: 0px 17px 24px 0px rgba(18, 83, 252, 0.51);
}
.get-button:hover i {
  padding-left: 5px;
}
.get-button.white {
  color: #ffffff;
}
.get-button.light {
  font-weight: 400;
}

.video-button-2 {
  width: 280px;
  height: 280px;
  border-radius: 50%;
  justify-content: center;
  overflow: hidden;
  align-items: center;
}
@media screen and (max-width: 400px) {
  .video-button-2 {
    width: 140px;
    height: 140px;
  }
}
.video-button-2 span {
  display: block;
  border-radius: 50%;
  width: 50px;
  height: 50px;
}
.video-button-2 span::after, .video-button-2 span::before {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 0.1px solid #5bc4e1;
  animation: videoTwo 8s linear infinite;
  -webkit-animation: videoTwo 8s linear infinite;
  -moz-animation: videoTwo 8s linear infinite;
}
.video-button-2 span:nth-child(1)::after {
  animation-delay: 1s;
  -webkit-animation-delay: 1s;
  -moz-animation-delay: 1s;
}
.video-button-2 span:nth-child(2)::after {
  animation-delay: 2s;
  -webkit-animation-delay: 2s;
  -moz-animation-delay: 2s;
}
.video-button-2 span:nth-child(3)::after {
  animation-delay: 3s;
  -webkit-animation-delay: 3s;
  -moz-animation-delay: 3s;
}
.video-button-2 span:nth-child(4)::after {
  animation-delay: 4s;
  -webkit-animation-delay: 4s;
  -moz-animation-delay: 4s;
}
.video-button-2 span:nth-child(1)::before {
  animation-delay: 5s;
  -webkit-animation-delay: 5s;
  -moz-animation-delay: 5s;
}
.video-button-2 span:nth-child(2)::before {
  animation-delay: 6s;
  -webkit-animation-delay: 6s;
  -moz-animation-delay: 6s;
}
.video-button-2 span:nth-child(3)::before {
  animation-delay: 7s;
  -webkit-animation-delay: 7s;
  -moz-animation-delay: 7s;
}
.video-button-2 i {
  width: 50px;
  height: 50px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  display: block;
  background: #2573d5;
  color: #ffffff;
  border: 5px solid #72a2dd;
}

@keyframes videoTwo {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(5);
    opacity: 0;
  }
}
.button-5 {
  border: none;
  border-radius: 10px;
  background: linear-gradient(0deg, #e2906e 0%, #e83a99 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  width: auto;
  padding: 10px 45px;
  color: #ffffff;
  margin-top: 20px;
}
.button-5:hover {
  color: #ffffff;
}

.preloader {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background-color: #081636;
  overflow: hidden;
}

.preloader-inner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.preloader-icon {
  width: 72px;
  height: 72px;
  display: inline-block;
  padding: 0px;
}

.preloader-icon span {
  position: absolute;
  display: inline-block;
  width: 72px;
  height: 72px;
  border-radius: 100%;
  background: #7c209c;
  animation: preloader-fx 1.6s linear infinite;
}

.preloader-icon span:last-child {
  animation-delay: -0.8s;
  -webkit-animation-delay: -0.8s;
}

@keyframes preloader-fx {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(1, 1);
    opacity: 0;
  }
}
/*All CSS of v-4.0.1 Starts Here*/
.border-cl-1 {
  border-color: rgba(104, 111, 197, 0.4) !important;
}

.section-header.cl-white .cl-white {
  color: #ffffff;
}

.stroke-title {
  -webkit-text-stroke: 1px rgba(0, 0, 0, 0.2);
  color: transparent;
  line-height: 1;
  text-align: center;
}
.stroke-title.cl-white {
  -webkit-text-stroke-color: rgba(255, 255, 255, 0.2);
  color: transparent;
}

.footer-top-2 {
  padding: 150px 0 100px;
}
@media (max-width: 1199px) {
  .footer-top-2 {
    padding: 100px 0 80px;
  }
}
@media (max-width: 1199px) {
  .footer-top-2 {
    padding: 80px 0 60px;
  }
}

.footer-stroke-text {
  margin-bottom: 100px;
}
.footer-stroke-text .stroke-title {
  font-size: 400px;
}
@media screen and (max-width: 1750px) {
  .footer-stroke-text {
    margin-bottom: 70px;
  }
  .footer-stroke-text .stroke-title {
    font-size: 300px;
  }
}
@media screen and (max-width: 1250px) {
  .footer-stroke-text {
    margin-bottom: 50px;
  }
  .footer-stroke-text .stroke-title {
    font-size: 220px;
  }
}
@media screen and (max-width: 991px) {
  .footer-stroke-text {
    margin-bottom: 20px;
  }
  .footer-stroke-text .stroke-title {
    font-size: 170px;
  }
}
@media (max-width: 767px) {
  .footer-stroke-text .stroke-title {
    font-size: 100px;
  }
}
@media (max-width: 575px) {
  .footer-stroke-text .stroke-title {
    font-size: 50px;
  }
}

.bg__change {
  background: rgba(255, 255, 255, 0.2);
}

.border-cl-2 {
  border-color: rgb(102, 124, 216) !important;
}

.pricing-item-16 ul.cl-white li {
  color: rgb(229, 227, 255);
}

.stroke-header {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: -80px;
  font-size: 200px;
  line-height: 1;
  font-weight: 900;
}
.stroke-header.stroke-right-top {
  top: 60px;
}
@media (min-width: 992px) {
  .stroke-header.stroke-right-top {
    left: 68%;
    top: 120px;
  }
}
@media screen and (max-width: 1250px) {
  .stroke-header {
    font-size: 120px;
    top: -60px;
  }
}
@media screen and (max-width: 991px) {
  .stroke-header {
    font-size: 80px;
    top: -40px;
  }
}
@media (max-width: 767px) {
  .stroke-header {
    font-size: 70px;
    top: -35px;
  }
}
@media (max-width: 575px) {
  .stroke-header {
    font-size: 45px;
    top: -30px;
  }
}

.bg-2 {
  background-color: rgb(238, 82, 83);
}

.client-say-16.change-maps::before {
  background: url(img/extra-2/map.png) no-repeat center center;
}

.stroke-counter-wrapper {
  justify-content: center;
  margin: -60px -15px -30px;
}
.stroke-counter-wrapper .stroke-counter-item {
  width: 33.3333333333%;
  padding: 0 15px 30px;
}
@media (max-width: 991px) {
  .stroke-counter-wrapper {
    margin-top: 0;
    padding-top: 60px;
  }
}
@media (max-width: 767px) {
  .stroke-counter-wrapper .stroke-counter-item {
    width: 50%;
  }
}
@media (max-width: 575px) {
  .stroke-counter-wrapper .stroke-counter-item {
    width: 100%;
  }
}

.stroke-counter-item {
  text-align: center;
}
.stroke-counter-item .title {
  font-size: 120px;
  font-weight: 700;
  color: transparent;
  -webkit-text-stroke: 1px #ffffff;
  line-height: 1;
  margin: 0;
  text-align: center;
  text-transform: uppercase;
}
.stroke-counter-item .stroke-counter-info {
  font-size: 18px;
  text-transform: uppercase;
  color: #ffffff;
}
@media (max-width: 1199px) {
  .stroke-counter-item .title {
    font-size: 80px;
  }
}
@media (max-width: 991px) {
  .stroke-counter-item .title {
    font-size: 60px;
  }
  .stroke-counter-item .stroke-counter-info {
    font-size: 16px;
  }
}

.apps-screenshot-section {
  background: #5718d3;
}

.apps-screenshot-section-2 {
  background: #9241f7;
}

.app-nav {
  margin: -5px;
  margin-top: 10px;
}
.app-nav .app-prev, .app-nav .app-next {
  width: 60px;
  height: 60px;
  cursor: pointer;
  text-align: center;
  line-height: 60px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 20px;
  display: block;
  margin: 5px;
}
.app-nav .app-prev:hover, .app-nav .app-next:hover {
  background: #ffffff;
}
.app-nav .app-prev:hover i, .app-nav .app-next:hover i {
  color: #111;
}
@media (max-width: 575px) {
  .app-nav .app-prev, .app-nav .app-next {
    width: 50px;
    height: 50px;
    line-height: 50px;
  }
}

.screenshot-slider-wrapper {
  max-width: 350px;
  position: relative;
  border-radius: 50px;
  box-shadow: -18.567px -25.963px 50px 0px rgba(117, 64, 222, 0.004), 25.963px 18.567px 70px 0px rgba(31, 9, 60, 0.45), inset 7.66px 6.428px 10px 0px rgba(95, 39, 205, 0.004), inset -5.142px -6.128px 14px 0px rgba(0, 37, 102, 0.3);
}
.screenshot-slider-wrapper::after {
  position: absolute;
  content: "";
  left: calc(100% + 30px);
  top: 0;
  bottom: 0;
  width: 1100px;
  background: url(img/extra-2/frame-shape.png) no-repeat center center;
  background-size: contain;
}
@media (max-width: 767px) {
  .screenshot-slider-wrapper {
    max-width: 300px;
  }
}
.screenshot-slider-wrapper.after-none::after {
  display: none;
}

.app-screenshot-thumb {
  width: 90px;
  height: 90px;
  padding: 9px;
  border-radius: 50%;
  border: 1px solid #cac7f6;
  position: relative;
  margin-bottom: 30px;
}
.app-screenshot-thumb::after, .app-screenshot-thumb::before {
  position: absolute;
  content: "";
  width: 10px;
  height: 10px;
  background-size: contain;
  border-radius: 50%;
  background: linear-gradient(0deg, #e2906e 0, #e83a99 100%);
  box-shadow: 1.21px 4.851px 6.72px 0.28px rgba(232, 58, 153, 0.0039);
  left: 5px;
  bottom: 5px;
}
.app-screenshot-thumb::after {
  width: 13px;
  height: 13px;
  left: auto;
  bottom: auto;
  right: 5px;
  top: 5px;
}
.app-screenshot-thumb .icon {
  background: linear-gradient(0deg, rgb(226, 144, 110) 0%, rgb(232, 58, 153) 100%);
  box-shadow: 2.419px 9.703px 12.48px 0.52px rgba(232, 58, 153, 0.5);
  width: 72px;
  height: 72px;
  border-radius: 50%;
  font-size: 40px;
  text-align: center;
  line-height: 65px;
}

.dots-2 {
  margin: -15px;
  margin-top: 10px;
}
.dots-2 .owl-dot {
  width: 30px;
  height: 30px;
  display: block;
  border-radius: 50%;
  position: relative;
  margin: 10px;
  cursor: pointer;
}
.dots-2 .owl-dot.active {
  border: 1px dashed #ffffff;
}
.dots-2 .owl-dot span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ffffff;
  display: block;
}
@media (max-width: 575px) {
  .dots-2 {
    margin: -5px;
    margin-top: 10px;
  }
  .dots-2 .owl-dot {
    margin: 5px;
  }
}

.banner-18 {
  padding: 200px 0 110px;
}
@media (max-width: 1399px) {
  .banner-18 {
    padding: 120px 0 90px;
  }
}
@media (max-width: 991px) {
  .banner-18 {
    padding: 160px 0 70px;
  }
}
@media (max-width: 767px) {
  .banner-18 .banner-thumb img {
    max-width: 100%;
  }
}

.banner-content-18 .title {
  font-size: 70px;
  line-height: 80px;
  margin-bottom: 15px;
}
.banner-content-18 p {
  font-family: "Josefin Sans", sans-serif;
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 38px;
}
@media (max-width: 1199px) {
  .banner-content-18 .title {
    font-size: 60px;
    line-height: 70px;
  }
}
@media (max-width: 767px) {
  .banner-content-18 .title {
    margin-bottom: 10px;
  }
  .banner-content-18 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-18 .title {
    font-size: 40px;
    line-height: 48px;
    margin-bottom: 10px;
  }
  .banner-content-18 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
}

.banner-17 {
  padding: 170px 0 162px;
}
@media (max-width: 1199px) {
  .banner-17 {
    background-position: bottom left;
    padding: 150px 0 100px;
  }
}
@media (max-width: 991px) {
  .banner-17 {
    background-position: bottom left;
    padding: 150px 0 70px;
  }
  .banner-17 .banner-thumb {
    margin: 40px auto 0;
  }
  .banner-17 .banner-thumb img {
    max-width: 100%;
  }
}

@media (min-width: 1200px) {
  .cl-xl-black {
    color: #ffffff !important;
  }
  .cl-xl-black .select-bar .current {
    color: #ffffff !important;
  }
  header.active .cl-xl-black.header-button {
    color: #ffffff !important;
  }
  header.active .cl-xl-black.header-button:hover {
    color: #ffffff !important;
  }
  header.active .cl-xl-black .select-bar .current {
    color: #ffffff !important;
  }
}
.amazing-slider {
  position: relative;
}
.amazing-slider::before {
  left: 0;
  right: 0;
  top: 100px;
  bottom: 100px;
  border-radius: 20px;
  background-image: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
}

@media (min-width: 768px) {
  .mt-md-120 {
    margin-top: 120px;
  }
}
.hightlight-link {
  font-weight: 600;
  font-size: 18px;
  color: #2253ff;
}

.feature--wrapper-17 {
  position: relative;
  padding-bottom: 20px;
}
.feature--wrapper-17::before {
  left: 150px;
  right: 150px;
  top: 50px;
  bottom: -60px;
  background: url(img/extra-2/feature-18.png) no-repeat center center;
  background-size: cover;
}
@media (max-width: 991px) {
  .feature--wrapper-17 {
    padding-bottom: 30px;
  }
  .feature--wrapper-17::before {
    bottom: -50px;
    left: 30px;
    right: 30px;
  }
}
@media (max-width: 767px) {
  .feature--wrapper-17 {
    padding-bottom: 30px;
  }
  .feature--wrapper-17::before {
    bottom: -40px;
  }
}

.amazing-flatform-thumb img {
  transform: translateX(113px);
}

.amazing-platform-content {
  position: relative;
}
.amazing-platform-content .subtitle {
  margin-bottom: 20px;
}

.amazing-platform-area {
  margin: 0 -15px -20px;
}

.amazing-platform-item {
  text-align: center;
  padding: 0 15px 24px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  font-family: "Josefin Sans", sans-serif;
}
.amazing-platform-item .amazing-platform-thumb {
  border-radius: 50%;
  background: #ffffff;
  width: 60px;
  height: 60px;
  line-height: 60px;
  margin: 0 auto 10px;
}
.amazing-platform-item .amazing-platform-thumb a i {
  border-radius: 50%;
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: linear-gradient(-103deg, rgb(239, 119, 76) 0%, rgb(237, 104, 79) 35%, rgb(232, 67, 81) 76%, rgb(231, 51, 81) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.video-tour-wrapper {
  border-radius: 20px;
  padding: 100px 15px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
@media (min-width: 992px) {
  .video-tour-wrapper {
    min-height: 600px;
  }
}
.video-tour-wrapper .ball-7 {
  opacity: 1;
}

.video-tour-item {
  align-items: center;
  padding: 13px;
  border-radius: 52px;
  background: #ffffff;
  position: relative;
  z-index: 9;
}
.video-tour-item .icon {
  width: 78px;
  height: 78px;
  line-height: 78px;
  text-align: center;
  border-radius: 50%;
  background: #6a4ee1;
  color: #ffffff;
  font-size: 24px;
}
.video-tour-item .cont {
  width: calc(100% - 78px);
  padding: 0 25px 0 20px;
  color: #bdb9f0;
  font-size: 14px;
}
.video-tour-item .cont .title {
  margin: 0;
  font-size: 18px;
}
@media (max-width: 575px) {
  .video-tour-item .icon {
    width: 50px;
    height: 50px;
    line-height: 50px;
    font-size: 20px;
  }
  .video-tour-item .cont {
    padding: 0 10px;
    width: calc(100% - 50px);
  }
}

.client-item-2 {
  align-items: center;
}
.client-item-2 .client-thumb {
  width: 350px;
}
.client-item-2 .client-thumb .thumb {
  height: 160px;
  width: 160px;
  border-radius: 50%;
  margin-bottom: 40px;
}
.client-item-2 .client-thumb .thumb img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.client-item-2 .client-thumb .title {
  text-transform: capitalize;
}
.client-item-2 .client-content {
  width: calc(100% - 350px);
  padding-left: 30px;
  padding-top: 120px;
  padding-bottom: 120px;
  position: relative;
}
.client-item-2 .client-content .quote {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.client-item-2 .client-content .quote img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
.client-item-2 .client-content .ratings {
  margin-bottom: 30px;
}
.client-item-2 .client-content .name {
  margin-bottom: 30px;
}
.client-item-2 .client-content p {
  font-family: "Josefin Sans", sans-serif;
  font-size: 24px;
  line-height: 38px;
  color: #ffffff;
}
@media (max-width: 991px) {
  .client-item-2 .client-thumb {
    width: 200px;
    font-size: 14px;
  }
  .client-item-2 .client-thumb .thumb {
    width: 90px;
    height: 90px;
  }
  .client-item-2 .client-thumb .title {
    font-size: 22px;
    line-height: 1.3;
  }
  .client-item-2 .client-content {
    width: calc(100% - 200px);
    padding-top: 70px;
    padding-bottom: 70px;
  }
}
@media (max-width: 767px) {
  .client-item-2 .client-thumb {
    width: 100%;
  }
  .client-item-2 .client-content {
    width: 100%;
    padding-left: 0;
    padding-top: 40px;
    padding-bottom: 40px;
    margin-top: 20px;
  }
  .client-item-2 .client-content .name {
    margin-bottom: 20px;
  }
  .client-item-2 .client-content p {
    font-size: 18px;
    line-height: 28px;
  }
}

.app-screenshot-slider-section {
  background: linear-gradient(-45deg, rgb(254, 65, 91) 0%, rgb(248, 118, 96) 100%);
}

.app-screenshot-slider-3-wrapper {
  position: relative;
}
.app-screenshot-slider-3-wrapper .app-nav .app-prev, .app-screenshot-slider-3-wrapper .app-nav .app-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: #ffffff;
  z-index: 9;
}
.app-screenshot-slider-3-wrapper .app-nav .app-prev:hover, .app-screenshot-slider-3-wrapper .app-nav .app-next:hover {
  color: #ee4730;
}
@media (max-width: 991px) {
  .app-screenshot-slider-3-wrapper .app-nav .app-prev, .app-screenshot-slider-3-wrapper .app-nav .app-next {
    top: unset;
    bottom: 250px;
  }
}
.app-screenshot-slider-3-wrapper .app-nav .app-prev {
  left: 0;
}
.app-screenshot-slider-3-wrapper .app-nav .app-next {
  right: 0;
}
.app-screenshot-slider-3-wrapper .screenshot-thumb {
  max-width: 350px;
  margin: 0 auto;
}

.social-icons.light-border-color li a {
  border-color: rgba(255, 255, 255, 0.2);
}

.footer-link.light-border-color li::after {
  background: rgba(255, 255, 255, 0.2);
}

.light-border {
  border-color: rgba(255, 255, 255, 0.2);
}

.mw-540px {
  max-width: 540px;
}

.apps-download-screen-20 {
  position: relative;
  margin-top: -380px;
}
.apps-download-screen-20 img {
  max-width: 100%;
}
.apps-download-screen-20 .apps-download-thumb {
  width: 120%;
  top: 60%;
  left: 56%;
}
.apps-download-screen-20 .apps-download-thumb img {
  max-width: 100%;
}
@media (max-width: 1199px) {
  .apps-download-screen-20 {
    margin-top: -340px;
  }
}
@media (max-width: 991px) {
  .apps-download-screen-20 {
    margin-top: -340px;
  }
  .apps-download-screen-20 .apps-download-thumb {
    top: 70%;
  }
}
@media (max-width: 767px) {
  .apps-download-screen-20 {
    margin-top: -250px;
  }
}

.apps-download-bg {
  animation: rotate3 6s alternate infinite;
}

@keyframes rotate3 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}
.apps-download-buttons-20 {
  position: relative;
  z-index: 9;
  margin-top: 150px;
}
.apps-download-buttons-20 .download-options {
  margin: 0 -15px -30px;
}
.apps-download-buttons-20 .download-options li {
  margin: 0 15px 30px;
}
.apps-download-buttons-20 .download-options li a {
  line-height: 96px;
  text-align: center;
  width: 96px;
  height: 96px;
  background: #bcbdf8;
  font-size: 46px;
}
.apps-download-buttons-20 .download-options li a i {
  background: #9d30fc;
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
}
.apps-download-buttons-20 .download-options li a:hover, .apps-download-buttons-20 .download-options li a.active {
  background: #ffffff;
}
@media (max-width: 767px) {
  .apps-download-buttons-20 .download-options li a {
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 24px;
  }
}
.apps-download-buttons-20 .download-options li span {
  display: block;
  text-align: center;
  margin-top: 10px;
}
@media (max-width: 991px) {
  .apps-download-buttons-20 {
    margin-top: 60px;
  }
}

.review-item-20 {
  background: #202342;
  border-radius: 20px;
  box-shadow: 2.925px 23.821px 38px 0px rgba(10, 25, 63, 0.28);
  padding: 25px 20px;
  margin-bottom: 30px;
}
.review-item-20 .review-item-header {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}
.review-item-20 .review-item-header .thumb {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
}
.review-item-20 .review-item-header .thumb img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.review-item-20 .review-item-header .content {
  padding-left: 20px;
  font-size: 14px;
}
.review-item-20 .review-item-header .content .title {
  margin: 0;
  font-weight: 500;
}
.review-item-20 .ratings {
  margin-bottom: 15px;
}
.review-item-20 blockquote {
  font-family: "Josefin Sans", sans-serif;
}
.review-item-20:hover, .review-item-20.active {
  background: linear-gradient(90deg, rgb(64, 80, 233) 0%, rgb(161, 47, 254) 100%);
  color: rgba(255, 255, 255, 0.9);
  animation: fadeIn 0.6s;
  -webkit-animation: fadeIn 0.6s;
  -moz-animation: fadeIn 0.6s;
}
.review-item-20:hover .title, .review-item-20.active .title {
  color: #ffffff;
}

@media (min-width: 992px) {
  .pt-lg-200 {
    padding-top: 200px;
  }
  .pb-lg-200 {
    padding-bottom: 200px;
  }
  .up--down--overflow {
    margin-bottom: -80px;
    margin-top: -80px;
  }
  .ml--lg-78 {
    margin-left: -78px;
  }
  .mr--lg-65 {
    margin-right: -65px;
  }
  .move-top--lg-70 {
    z-index: 9;
    position: relative;
    transform: translateY(-70px);
  }
}
.body--bg {
  background: #202342;
}

.pricing-item-20 {
  text-align: center;
  border-radius: 30px;
  background: linear-gradient(-65deg, rgb(193, 101, 221) 0%, rgb(17, 83, 252) 100%);
  margin-bottom: 30px;
  margin-left: 40px;
  position: relative;
}
.pricing-item-20::before {
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: url(img/extra-2/pricing.png) no-repeat;
  background-position: -54px 0;
  background-size: cover;
  border-radius: 30px;
}
.pricing-item-20 .pricing-body, .pricing-item-20 .pricing-footer, .pricing-item-20 .pricing-header {
  z-index: 9;
  position: relative;
}
.pricing-item-20 .pricing-header {
  color: #ffffff;
  padding-bottom: 30px;
}
.pricing-item-20 .pricing-header .name {
  font-weight: 600;
  margin: 0 auto;
  border-radius: 0 0 30px 30px;
  line-height: 40px;
  display: inline-block;
  width: auto;
  margin: 0 auto 40px;
  background: #31377d;
  color: #ffffff;
  min-width: 160px;
  padding: 0 15px;
}
.pricing-item-20 .pricing-header .price {
  color: #ffffff;
  font-weight: 400;
  margin: 0;
  line-height: 1;
}
.pricing-item-20 .pricing-header .info {
  text-transform: uppercase;
}
.pricing-item-20 .pricing-body {
  font-family: "Josefin Sans", sans-serif;
  background-color: #31377d;
  box-shadow: 1.95px 15.881px 30px 0px rgba(47, 105, 252, 0.3);
  margin-left: -10px;
  transform: translateX(-30px);
  padding: 20px 40px 20px 65px;
  border-radius: 0 30px 30px 0;
  position: relative;
}
.pricing-item-20 .pricing-body::before {
  position: absolute;
  width: 40px;
  height: 48px;
  background-color: #31377d;
  left: 0;
  top: 100%;
  content: "";
  -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%);
          clip-path: polygon(0 0, 100% 0, 100% 100%);
}
.pricing-item-20 .pricing-body ul li {
  line-height: 28px;
  padding: 8px 0;
  color: #ffffff;
}
.pricing-item-20 .pricing-body ul li:not(:last-child) {
  border-bottom: 2px dotted #bccaea;
}
.pricing-item-20 .pricing-footer {
  padding: 40px 15px;
}
@media screen and (max-width: 500px) {
  .pricing-item-20 {
    margin-left: 20px;
  }
  .pricing-item-20 .pricing-body {
    margin-left: 0;
    transform: translateX(-20px);
    padding: 20px;
    font-size: 16px;
  }
  .pricing-item-20 .pricing-body::before {
    width: 20px;
  }
  .pricing-item-20 .pricing-footer {
    padding: 30px 10px;
  }
  .pricing-item-20 .pricing-footer a {
    font-size: 14px;
    padding: 10px 40px;
  }
}

div[class*=col]:nth-of-type(3n + 1) .pricing-item-20 {
  background: linear-gradient(0deg, rgb(254, 100, 139) 0%, rgb(255, 146, 111) 100%);
  background: -webkit-linear-gradient(0deg, rgb(254, 100, 139) 0%, rgb(255, 146, 111) 100%);
}
div[class*=col]:nth-of-type(3n + 3) .pricing-item-20 {
  background: linear-gradient(0deg, rgb(1, 183, 205) 0%, rgb(2, 203, 122) 100%);
  background: -webkit-linear-gradient(0deg, rgb(1, 183, 205) 0%, rgb(2, 203, 122) 100%);
}

@media (min-width: 992px) {
  .apps-screenshot-section-20 {
    padding: 260px 0;
  }
}

.app-screenshot-20 {
  position: absolute;
  top: 0;
  right: calc(50% - 140px);
  height: 100%;
}
.app-screenshot-20 img {
  height: 100%;
}

.download-option-20 {
  margin: -15px;
  text-align: center;
}
.download-option-20 li {
  margin: 15px;
}
.download-option-20 li a {
  width: 95px;
  height: 95px;
  line-height: 95px;
  font-size: 48px;
  text-align: center;
}
.download-option-20 li a.active, .download-option-20 li a:hover {
  background: linear-gradient(90deg, rgb(64, 80, 233) 0%, rgb(161, 47, 254) 100%);
}
@media (max-width: 767px) {
  .download-option-20 li a {
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 24px;
  }
}
.download-option-20 li span {
  font-weight: 600;
  font-family: "Josefin Sans", sans-serif;
  margin-top: 15px;
  display: block;
}

.app-screenshot-slider-20 {
  margin-right: 45px;
}

.feature-thumb-20 {
  margin-bottom: -100px;
  margin-top: -80px;
}

.feature-content-20 {
  padding: 60px 15px 60px 0;
}
.feature-content-20 .title {
  margin-bottom: 27px;
}
.feature-content-20 .feat-nav a {
  background: rgba(255, 255, 255, 0.2);
}
.feature-content-20 .feat-nav a:hover, .feature-content-20 .feat-nav a.active {
  background: linear-gradient(-103deg, rgb(239, 119, 76) 0%, rgb(237, 104, 79) 35%, rgb(232, 67, 81) 76%, rgb(231, 51, 81) 100%);
}

.feature-content-icon-20 {
  width: 123px;
}

.feature-wrapper-bg-20 {
  border-radius: 20px;
  margin-top: 80px;
  margin-bottom: 100px;
}
@media (max-width: 991px) {
  .feature-wrapper-bg-20 {
    margin: 0;
  }
  .feature-wrapper-bg-20 .feature-content-20 {
    padding: 60px 30px;
  }
}
@media (max-width: 575px) {
  .feature-wrapper-bg-20 .feature-content-20 {
    padding: 30px 15px;
  }
}

.how-slider-wrapper {
  position: relative;
}
.how-slider-wrapper .dots-2 {
  position: absolute;
  left: 0;
  justify-content: center;
  width: 100%;
  bottom: 20%;
  z-index: 9;
  margin: -5px;
}
.how-slider-wrapper .dots-2 .owl-dot {
  border: 0;
  margin: 0;
}
.how-slider-wrapper .dots-2 .owl-dot.active span {
  background: #ee4730;
}

.how-item-20 {
  display: flex;
  align-items: center;
  border-radius: 50%;
  width: 230px;
  position: relative;
  z-index: 9;
  margin-left: auto;
}
.how-item-20 .how-thumb {
  width: 106px;
  height: 106px;
  border-radius: 50%;
  box-shadow: 16.773px 10.893px 43px 0px rgba(60, 49, 172, 0.39);
  border-radius: 50%;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
}
.how-item-20 .how-thumb img {
  max-width: 100%;
}
.how-item-20 .title {
  font-size: 18px;
  padding-right: 12px;
  width: calc(100% - 106px);
  margin: 0;
}
@media (min-width: 992px) {
  .how-item-20:nth-child(1) {
    margin-bottom: 20px;
    margin-right: -85px;
  }
  .how-item-20:nth-child(2) {
    margin-right: -145px;
  }
  .how-item-20:nth-child(3) {
    margin-right: -235px;
  }
}
@media (min-width: 1200px) {
  .how-item-20:nth-child(1) {
    margin-bottom: 20px;
    margin-right: -105px;
  }
  .how-item-20:nth-child(2) {
    margin-right: -185px;
  }
  .how-item-20:nth-child(3) {
    margin-right: -325px;
  }
}

@media (max-width: 991px) {
  .how-item-wrapper-20 {
    display: flex;
    flex-wrap: wrap;
  }
  .how-item-wrapper-20 .how-item-20 {
    margin: 0;
    width: unset;
    padding: 0 15px 20px;
    flex-direction: row-reverse;
  }
  .how-item-wrapper-20 .how-item-20 .title {
    padding: 0 0 0 12px;
  }
  .how-item-wrapper-20 .how-item-20 .how-thumb {
    width: 60px;
    height: 60px;
  }
  .how-item-wrapper-20 .how-item-20 .how-thumb img {
    width: 35px;
    height: 35px;
    -o-object-fit: contain;
       object-fit: contain;
  }
  .how-item-wrapper-20 .how-item-20 .title {
    width: unset;
  }
}
/*Banner Section 20 Starts Here*/
.banner-thumb-20 {
  position: relative;
}
.banner-thumb-20 .video-button {
  position: absolute;
  left: calc(100% - 25px);
  top: calc(50% - 25px);
  background: -ms-linear-gradient(-45deg, rgb(193, 101, 221) 0%, rgb(92, 39, 254) 100%);
}
.banner-thumb-20 .video-button i {
  color: #ffffff;
  position: relative;
  z-index: 3;
}
.banner-thumb-20 .video-button::after, .banner-thumb-20 .video-button::before {
  opacity: 0.5;
  background: -webkit-linear-gradient(-65deg, #c165dd 0%, #1153fc 100%);
}
@media (max-width: 991px) {
  .banner-thumb-20 {
    max-width: 540px;
    margin: 40px auto 0;
  }
  .banner-thumb-20 img {
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .banner-thumb-20 {
    max-width: 400px;
  }
}
@media (max-width: 575px) {
  .banner-thumb-20 {
    max-width: 80%;
  }
}

.banner-content-20 {
  position: relative;
}
@media (min-width: 992px) {
  .banner-content-20 {
    margin-bottom: 50px;
  }
}
.banner-content-20 .banner-button-group .play-button {
  color: #ffffff;
}
.banner-content-20 .banner-button-group .play-button img {
  width: 60px;
}
.banner-content-20 .title {
  font-size: 76px;
  line-height: 86px;
  margin-bottom: 15px;
}
.banner-content-20 p {
  font-size: 24px;
  line-height: 34px;
  margin: 0;
  margin-bottom: 33px;
}
@media (max-width: 767px) {
  .banner-content-20 .title {
    font-size: 60px;
    line-height: 70px;
    margin-bottom: 10px;
  }
  .banner-content-20 p {
    font-size: 20px;
    line-height: 30px;
    margin-bottom: 35px;
  }
}
@media (max-width: 575px) {
  .banner-content-20 .title {
    font-size: 48px;
    line-height: 56px;
    margin-bottom: 10px;
  }
  .banner-content-20 p {
    font-size: 18px;
    line-height: 30px;
    margin-bottom: 25px;
  }
}

.banner-20 {
  padding: 200px 0 85px;
  background: #202342;
}
@media (max-width: 1199px) {
  .banner-20 {
    padding: 130px 0 75px;
  }
}

.section--bg {
  background: #31377d;
}